<template>

    <el-container class="el-container-scoped">
      <el-aside tag="div" class="el-aside-scoped" v-if="!hiddenSidebar"  style="width:auto;">
          <v-sidebar :modules="currentModule==null?[]:currentModule.children" @beforemoduleclick="beforemoduleclick" @moduleclicked="moduleclicked"></v-sidebar>
      </el-aside>
      <el-main ref="workbench"  class="el-main-scoped">
        <el-tabs v-model="currentTab" closable tab-position="top" type="card" @tab-click="handleTabClick" @edit="handleTabEdit" class="el-tabs-scoped">
            <el-tab-pane
                v-for="(item, index) in pageMultiTabs"
                :key="item.name"
                :label="item.title"
                :name="item.name"
              >
                <span v-if="typeof(item.content) == 'string'">{{item.content}}</span>
                <component v-bind="item.props" v-else-if="typeof(item.content) == 'function'||typeof(item.content)=='object'" :is="item.content" ></component>
              </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>  
</template>
<script>
import vSidebar from "./Sidebar.vue";
import hash from "../../lib/hash";
import {mixin,runtimeCfg} from "../../assets/core/index";

export default {
  components: {
    vSidebar
  },
  mixins:[mixin],
  props:{
    indexPath:{
      type:String,
      default:"",
    }
  },
  data() {
    return {
      hiddenSidebar: this.$route.query.hasOwnProperty("hidsid")?true:runtimeCfg.page_hid_sidebar,
      pageMultiTabs: [],
      currentTab:hash(this.indexPath),
    };
  },
  computed: {
    modulesTree : function(){return this.auth.instance.getModulesTree()},
    currentModuleResId : function(){return this.$route.matched[1].path},//this.$route.matched[1] 地址匹配的是路由的第二层，第一层为"/"
    currentModule : function(){
      if(!this.currentModuleResId) return null;
      for(let i=0;i<this.modulesTree.length&&this.modulesTree.length>0;i++)
      {
        if(this.modulesTree[i].resPvalue.indexOf(this.currentModuleResId+"")>-1)
        {
          return this.modulesTree[i];
        }
      }
      return null;
        
    },
  },
  methods:{


    /**
     * 侧边栏点击前处理事件，
     * 
     */
    beforemoduleclick(routePath){

    },

    /**
     * 侧边栏点击后处理事件，
     * 
     */
    moduleclicked(routePath)
    {
      //获取路由定义信息
      let routeDef=this.$router.match(routePath);
      //根据路由获取组件定义列表
      //let componentDefs=this.$router.getMatchedComponents(routePath);
      //根据路由获取最后一个组件定义，即当前路由地址对应的最终组件
      //let componentDef=componentDefs[componentDefs.length-1];
      //获取组件默认构造
      let componentInfo=routeDef.matched[routeDef.matched.length-1].components.default;
      //获取组件默认构造参数props
      let componentProps=routeDef.matched[routeDef.matched.length-1].props.default;
      componentProps= (componentProps===true?componentProps:
      (typeof(componentProps)=="object"?componentProps:
      (typeof(componentProps)=="function"?componentProps(this.$route):"")))

      let module={};
      for(let resItem of this.auth.instance.getPermission().resList.values())
      {
        if(resItem.resPvalue==routePath)
        {
          module=resItem;
          break;
        }
      }
      this.addTab(routeDef.meta.title+(module&&module.resName?"["+module.resName+"]":""),
      routePath,
      componentInfo,
      componentProps)
    },
    /**
     * 添加tab选项卡
     * @param string title  tab选项卡文字
     * @param string name  tab唯一识别
     * @param vue content  tab-content所要加载的vue组件对象
     * @param json props  要加载的vue对象的参数
     */
    addTab(title,name,content,props)
    {
      name=hash(name);
      
      for(let tabItem of this.pageMultiTabs.values())
      {
        if(tabItem.name==name) {
          this.currentTab=tabItem.name;
          return;
        }
      }
      this.pageMultiTabs.push({
        title,
        "name":name,
        content,
        props});
      this.currentTab=name;
      
    },
    handleTabClick(tab, event) {
      console.log(tab, event);
    },
    handleTabEdit(tabname, event) {
      if(event=="remove"){
        let removeIndex=-1;
        for(let i=0;i<this.pageMultiTabs.length;i++)
        {
          if(this.pageMultiTabs[i].name==tabname) {
            removeIndex=i;
            break;
          }
        }
        if(removeIndex>=0){
          this.pageMultiTabs.splice(removeIndex,1);
        }
        if(this.pageMultiTabs.length>0)
        {
          this.currentTab=this.pageMultiTabs[this.pageMultiTabs.length-1].name;
        }
        
      }
    },
  },
  mounted(){
    this.$EventBus.$on("moduleclicked", (routePath) => {
       this.moduleclicked(routePath);
    });
    //加载多页面模式的首页
    if(this.indexPath!=""){
      this.moduleclicked(this.indexPath);
    }
    //加载路由指向的当前页（首页与当前访问的不是同一地址的情况下）
    else if(this.indexPath!=this.$route.path){
      this.moduleclicked(this.$route.path);
    }
  }
};
</script>
  