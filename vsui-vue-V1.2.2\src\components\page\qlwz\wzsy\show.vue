<!-- 360 兼容是使用的IE内核，vue对IE的兼容性不好，特别是VUE3之后不提供对IE的支持了 -->
<template>
    <div class="showClass">
        <!-- 展示图片 -->
        <div class="image-container">
            <img v-for="(item, index) in listData" :key="index"
                :src="'/backend/vscomponent/fileupload/show?id=' + item.id"
                :class="['image-item']" @click="setVideo(item, index)" />

        </div>
    </div>
</template>
<script>
export default {
    watch: {
        $route: {
            handler(oldVal) {
                this.loadListData();
            },
            deep: true
        },
    },
    data() {
        return {
            listData: [],
            listQuery: {
                pageIndex: 1,
                pageSize: 10,
            },
            currentVideoId: '',//接受查到的视频的主键id
            bigPicture: null, // 当前放大的图片索引
            playerOptions: {
                playbackRates: [0.7, 1.0, 1.5, 2.0], // 播放速度
                autoplay: false, // 如果true,浏览器准备好时开始回放。
                muted: false, // 默认情况下将会消除任何音频。
                loop: false, // 导致视频一结束就重新开始。
                preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
                language: 'zh-CN',
                aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
                fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
                sources: [{
                    type: 'video/mp4', // 类型
                    src: '' // url地址
                }],
                poster: '', // 封面地址
                notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
            },
        }
    },
    mounted() {
        this.listLoading();
    },
    methods: {
        //通过上传文件的业务id查询出类型是视频类型的信息
        setVideo(item, index) {
            console.log("@@@@@@@@@@@@item", item);
            this.axios({
                method: 'post',
                url: '/backend/qlwz/queryVideo',
                data: item,
            }).then(response => {
                if (response.data.data) {
                    this.currentVideoId = response.data.data.id;
                    item.currentVideoId = this.currentVideoId;

                    this.bigPicture = index; // 更新当前放大的图片索引
                    
                    localStorage.setItem('itemData', JSON.stringify(item));
                    const routeData = this.$router.resolve({
                        name: 'detail',
                        params: this.playerOptions
                    });
                    window.open(routeData.href, '_blank');
                }

            })
        },
        //加载纪法课堂的前三条最新信息
        listLoading() {
            this.axios({
                method: 'post',
                url: '/backend/qlwz/queryInfoData',
                data: this.listQuery,
            }).then(response => {
                if (response.data.data) {
                    this.listData = response.data.data.rows;
                }

            }).catch(() => {
                this.$message('查询失败！')
            })
        },

    }
}
</script>
<style scoped>
.showClass {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.image-container {
    display: flex;
    justify-content: center;
    width: 1000px;
    /* 与视频播放器相同的宽度 */
    margin-top: 10px;
    margin-bottom: 10px;
}

.image-item {
    width: calc(33.333% - 10px);
    margin-top: 5px;
    margin-right: 50px;
    transition: transform 0.3s ease;
    cursor: pointer;
    /* 鼠标悬停时显示小手 */
}

.image-item:last-child {
    margin-right: 0;
}


</style>