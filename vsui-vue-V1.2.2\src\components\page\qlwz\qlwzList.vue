<template>
    <div class="qlwz-list-container">
        <div class="qlwz-list-main">
            <div class="qlwz-list-searchArea">
                <h3 v-if="type != 'JFKT'" style="text-align: center;">信息填报</h3>
                <h3 v-if="type == 'JFKT'" style="text-align: center;">纪法课堂</h3>
                <el-row>
                    <el-col :span="18">
                        <label>标题:</label>
                        <el-input @keyup.enter.native="processSearch" style="width: 200px;" class="filter-item"
                            placeholder="标题" v-model="listQuery.infoTitle" clearable>
                        </el-input>
                        <el-button type="primary" icon="el-icon-search" style="margin-left:8px;"
                            @click="processSearch">查询</el-button>
                    </el-col>
                    <el-col :span="6" style="text-align:right">
                        <el-button v-if="type != 'JFKT'" type="success" icon="el-icon-plus"
                            @click="openDialog(null, 'add', '新增门户信息')">新增</el-button>
                    </el-col>
                    <el-col :span="6" style="text-align:right">
                        <el-button v-if="type == 'JFKT'" type="success" icon="el-icon-plus"
                            @click="openDialog(null, 'addJFKT', '新增纪法课堂')">新增</el-button>
                    </el-col>
                </el-row>
            </div>
            <el-row class="qlwz-list-tableArea">
                <el-table border v-loading="listLoading" :data="tableData" tooltip-effect="dark"
                    :header-cell-style="{ background: '#F4F7FA' }">
                    <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px">
                    </el-table-column>
                    <el-table-column min-width="20%" align="left" header-align="center" label="标题" prop="infoTitle" show-overflow-tooltip>

                    </el-table-column>
                    <el-table-column min-width="10%" align="center" header-align="center" label="发布时间"
                        prop="publishTime">

                    </el-table-column>
                    <el-table-column v-if="type != 'JFKT'" min-width="10%" align="center" header-align="center"
                        label="审核节点" prop="processStatus">
                        <template slot-scope="scope">
                            <!-- 根据processStatus的值返回不同的字符串 -->
                            <span v-if="scope.row.processStatus === '0'">草稿</span>
                            <span v-if="scope.row.processStatus === '1'">纪检委员审核</span>
                            <span v-if="scope.row.processStatus === '2'">纪检部审核</span>
                            <span v-if="scope.row.processStatus === '3'">完成</span>
                            <span v-if="scope.row.processStatus === '4'">退回</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="this.type == 'JFKT'" min-width="10%" align="center" header-align="center"
                        label="发布人" prop="operatorName">
                    </el-table-column>
                    <el-table-column v-if="this.type != 'JFKT'" min-width="10%" align="center" header-align="center"
                        label="审核意见" prop="oponion" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column min-width="10%" label="操作" align="center" header-align="center">
                        <template slot-scope="scope">
                            <el-button
                                v-if="(type != 'JFKT' && (scope.row.infoStatus == '0' || scope.row.processStatus == '4')) || (type == 'JFKT' && scope.row.infoStatus == '0')"
                                size="mini" type="text" icon="el-icon-edit"
                                @click="openDialog(scope.row, type === 'JFKT' ? 'editJFKT' : 'edit', type === 'JFKT' ? '编辑纪法课堂' : '编辑门户信息')">编辑</el-button>

                            <!-- 删除按钮 -->
                            <el-button v-if="scope.row.infoStatus == '0'" size="mini" type="text" icon="el-icon-delete"
                                @click="deleteData(scope.row)">删除</el-button>

                            <!-- 查看按钮 -->
                            <el-button
                                v-if="(scope.row.processStatus === '1' || scope.row.processStatus === '2' || scope.row.processStatus === '3')"
                                size="mini" type="text" icon="el-icon-view"
                                @click="openDialog(scope.row, type === 'JFKT' ? 'viewJFKT' : 'view', '查看信息')">查看</el-button>

                        </template>
                    </el-table-column>
                </el-table>
                <div class="qlwz-list-paginationArea">
                    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :page-sizes="[10, 20, 30, 50]" :page-size="listQuery.pageSize"
                        :current-page="listQuery.pageIndex" layout="total, sizes, prev, pager, next, jumper"
                        :total="total"></el-pagination>
                </div>
            </el-row>

        </div>
        <!-- 以下是信息门户的弹出页 -->
        <el-dialog v-dialogDrag v-if="dialog" width="35%" :title="Title" :visible.sync="dialog"
            :before-close="handleCloseViewDialog" :close-on-click-modal="false">
            <addList :operation="operation" :rowData="rowData" :type="type"
                @handleCloseViewDialog="handleCloseViewDialog" />
        </el-dialog>
        <!-- 以下是纪法课堂信息的弹出页 -->
        <el-dialog v-dialogDrag v-if="JFKTDialog" width="35%" :title="Title" :visible.sync="JFKTDialog"
            :before-close="handleCloseViewDialog" :close-on-click-modal="false">
            <addListJfkt :operation="operation" :rowData="rowData" :type="type"
                @handleCloseViewDialog="handleCloseViewDialog" />
        </el-dialog>
    </div>
</template>
<script>
import addList from './addList.vue';
import addListJfkt from './addListJfkt.vue'
import VSAuth from "@vsui/lib-vueauth4vseaf";
export default {
    components: {
        addList, addListJfkt
    },
    props: {

        type: {
            type: String,
            default: ""
        },
    },
    watch: {
        $route: {
            handler(oldVal) {
                this.loadListData();
            },
            deep: true
        },
    },
    data() {
        return {
            rowData: {},
            total: null,
            tableData: [],
            listLoading: true,
            listQuery: {
                pageIndex: 1,
                pageSize: 10,
                infoTitle: '',
                operatorId: '',
                infoType: '',

            },
            //对话框相关变量
            dialog: false,
            Title: '',
            operation: '',
            JFKTDialog: false,
        }
    },
    mounted() {
        this.loadListData();

    },
    methods: {
        //删除数据
        deleteData(row) {
            this.$confirm("此操作将永久删除当前数据，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.axios({
                    method: "get",
                    url: "/backend/qlwz/deleteInfoData",
                    params: { infoId: row.infoId }
                }).then(resp => {
                    if (resp.data.data.status == 'success') {
                        this.$message.success("删除成功！");
                        this.loadListData();
                    } else {
                        this.$message.error(resp.data.data.message);
                    }

                }).catch(error => {
                    this.$message.error("删除失败");
                })
            })

        },
        //加载数据列表
        loadListData() {
            this.listQuery.infoType = this.type;
            this.listQuery.operatorId = VSAuth.getAuthInfo().permission.userId;
            this.axios.post('/backend/qlwz/queryInfoDataList', this.listQuery
            ).then(resp => {
                if (resp.data.data) {
                    this.tableData = resp.data.data.rows;
                    this.total = resp.data.data.total;
                    this.listLoading = false;
                }
            }).catch((error) => {
                this.$message.error('查询失败')
                this.listLoading = false;
            });

        },
        // 打开对话框页面
        openDialog(row, flag, title) {
            this.operation = flag;
            this.rowData = row;
            this.Title = title;
            if (flag === 'editJFKT' || flag === 'addJFKT' || flag === 'viewJFKT') {
                this.JFKTDialog = true;

            } else {
                this.dialog = true;
            }
        },

        // 弹出页面点击关闭按钮回调函数
        handleCloseViewDialog() {
            this.dialog = false;
            this.JFKTDialog = false;
            this.loadListData();
        },

        //查询数据
        processSearch() {
            this.listQuery.pageIndex = 1;
            this.loadListData();
        },
        // 变更每页条数时自动加载数据
        handleSizeChange(val) {
            this.listQuery.pageSize = val;
            this.loadListData();
        },

        // 点击某一页时自动加载数据
        handleCurrentChange(val) {
            this.listQuery.pageIndex = val;
            this.loadListData();
        },



    }
}
</script>
<style scoped></style>