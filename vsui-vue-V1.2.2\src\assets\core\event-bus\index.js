import Vue from "vue";
/**
 * Vue全局事件总线，用于在组件间进行事件通知，请用观察者模式进行事件监听
 * Created in 20200118 by cuiliang 崔良于2020年1月18日，公司18周年纪念
 * 使用方法：
 * 在vue页面中无引入内容，触发事件使用以下代码
 * this.$EventBus.$emit("事件名称",事件参数1,事件参数2....,事件参数n);
 * 接收事件使用如下代码
 * this.$EventBus.$on("事件名称",(事件参数1,事件参数2....,事件参数n)=>{
 *  你想要的处理写在这里
 * })
 *
 */
 Vue.use(
     {
         install(vue){
            vue.prototype.$EventBus = new vue();
         }
     }
 )

 export default Vue.prototype.$EventBus;