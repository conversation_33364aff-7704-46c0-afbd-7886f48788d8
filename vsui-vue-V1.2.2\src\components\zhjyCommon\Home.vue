<template>
  <div class="wrapper">
      <div class="content-box" :class="{'content-collapse':collapse,'content-box-hiddenHeader':hiddenHeader==''}">
        <div class="content">
          <transition name="move" mode="out-in">
              <router-view></router-view>
          </transition>
        </div>
      </div>
  
  </div>
</template>

<script>
//import vFooter from "./Footer.vue";
import vHeader from "./Header.vue";
import vFooter from "./Footer.vue";
import {runtimeCfg} from "../../assets/core/"

export default {
  data() {
    return {
      tagsList: [],
    };
  },
  components: {
    vHeader,
    vFooter,
    runtimeCfg
  },
  created() {
 
  },
  mounted() {
    let _this=this;
    document.addEventListener("click", function (e) {
      _this.$EventBus.$emit("documentclick",e);
    });
  },


};
</script>
