<template>
    <div class="zhyy-edit-container">
        <div class="el-dialog__header" ><span class="el-dialog__title">新增表单信息</span></div>

        <div class="zhyy-edit-formArea" >
            <el-form ref="dataForm" :model="formData" :rules="rules" style="padding:16px;height: calc(100vh - 180px);overflow: auto;">
                <fieldset>
                    <legend>基本控件</legend>  
                    <el-row >
                        <el-col :span="12">
                            <el-form-item label-width="120px" label="输入框" prop="SRK">
                                <el-input v-model="formData.SRK" placeholder="请输入表单名称" clearable :style="{width: '100%'}"
                                    :maxlength="100" show-word-limit>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label-width="120px" label="下拉框" prop="XLK">
                                <el-select v-model="formData.XLK" placeholder="请选择下拉框" :style="{width: '100%'}">
                                    <el-option v-for="(item, index) in LBOptions" :key="index" :label="item.label"
                                        :value="item.value" :disabled="item.disabled"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    
                    </el-row>
                    <el-row >
                        <el-col :span="12">
                            <el-form-item label-width="120px" label="日期框" prop="RQK">
                                <el-date-picker type="date" placeholder="选择日期" v-model="formData.RQK"
                                    style="width: 100%" value-format="yyyy-MM-dd" v-if="!isView"></el-date-picker>

                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label-width="120px" label="数字框" prop="SZK" >
                                <el-input-number
                                    type="number"
                                    max="1000"
                                    min="0"
                                    precision="2"
                                    :controls="false"
                                    controls-position="right"
                                    v-model="formData.SZK"
                                    :style="{width: '100%'}"
                                ></el-input-number>
                                
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row >
                        <el-col :span="12">
                            <el-form-item label-width="120px" label="单选框" prop="DXK">
                                <el-radio v-model="formData.DXK" label="1" >选项1</el-radio>
                                <el-radio v-model="formData.DXK" label="2" >选项2</el-radio>

                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label-width="120px" label="多选框" prop="FXK" >
                                <el-checkbox v-model="formData.FXK">备选项</el-checkbox>
                                
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row >
                        <el-col :span="24">
                            <el-form-item label-width="120px" label="文本区域" prop="WBQY">
                                <el-input v-model="formData.WBQY" type="textarea" placeholder="文本区域"
                                    :autosize="{minRows: 4, maxRows: 8}" :style="{width: '100%'}" :maxlength="4000" show-word-limit>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </fieldset>
               
                <fieldset>
                    <legend>扩展控件信息</legend>  
                    <el-row >
                        <el-col :span="12">
                            <el-form-item label-width="120px" label="下拉树" prop="XLS_PATH" >
                                <el-cascader style="width: 100%"
                                    ref="XLS_PATH"
                                    :options="xlTreeList" 
                                    :show-all-levels="false" 
                                    :props="xlProps"
                                    @change="handleChangeXl($event)"
                                    v-model="formData.XLS_PATH"
                                    placeholder="请选择" filterable clearable >
                                </el-cascader>
                                    
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label-width="120px" label="日期区间" prop="RQQJ">
                                <el-date-picker
                                    value-format="yyyy-MM-dd"
                                    format="yyyy-MM-dd"
                                    v-model="RQQJ"
                                    type="daterange"
                                    align="right"
                                    unlink-panels
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    @change="chooseTimeRange"
                                    
                                    >
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        
                    </el-row>

                    <el-row >
                        <el-col :span="12">
                            <el-form-item label="弹出列表选择" prop="TCXZ" label-width="120px" >
                                <el-input v-model="formData.TCXZ" style="width: 100%" readonly
                                placeholder="点击按钮选择" >
                                    <el-button 
                                    slot="append" 
                                    type="primary" 
                                    @click="popListSelect" 
                                    icon="el-icon-circle-plus-outline"></el-button>
                                </el-input> 
                                
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="简单多选" prop="JDDX" label-width="120px" >
                                <el-input v-model="formData.JDDX" style="width: 100%" readonly
                                placeholder="点击按钮选择" >
                                    <el-button 
                                    slot="append" 
                                    type="primary" 
                                    @click="popTransferSelect" 
                                    icon="el-icon-circle-plus-outline"></el-button>
                                </el-input> 
                                
                            </el-form-item>

                            
                        </el-col>
                    </el-row>

                    <el-row >
                        <el-col :span="12">
                            <el-form-item label="弹出列表多选" prop="TCDX" label-width="120px" >
                                <el-input v-model="formData.TCDX" style="width: 100%" readonly
                                placeholder="点击按钮选择" >
                                    <el-button 
                                    slot="append" 
                                    type="primary" 
                                    @click="popWlxxSelect" 
                                    icon="el-icon-circle-plus-outline"></el-button>
                                </el-input> 
                                
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            
                        </el-col>
                    </el-row>


                    <el-row >
                        <el-col :span="12">
                            <el-form-item label="附件上传" prop="FJSC" label-width="120px" >
                                <uploadFile 
                                    :type="'file'"
                                    :limit="fileLimit"
                                    :btnMsg="'点击上传'"
                                    :ywid="formData.FJSC"
                                    :fjlx="'RDSQSFJ'"
                                    :xtmk="'1'"
                                    v-if="formData.FJSC != ''" 
                                    :isView="false"
                                    
                                ></uploadFile>
                                
                            </el-form-item> 
                        </el-col>
                        <el-col :span="12">
                            
                        </el-col>
                    </el-row>
                </fieldset>

                <fieldset>
                    <legend>子表信息</legend>  
               
                    <el-row >
                        <el-col :span="24" style="text-align:right;padding-bottom:8px">
                            <el-button size='mini' type='success' icon="el-icon-plus" @click="addHb">新增</el-button>
                            <el-button size='mini' type='danger' icon="el-icon-delete" @click="deleteHb">删除</el-button>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-table :data="sxhbList" stripe highlight-current-row  @current-change="handleCurrentChange">
                                <el-table-column prop="pxh" label="序号" width="60" align="center">
                                </el-table-column>

                                <el-table-column label="汇报标题" width="370" align="center">
                                    <template slot-scope="scope">
                                        <el-input placeholder="汇报标题"
                                            v-model="scope.row.hbnr" value-format="yyyy-MM-dd">
                                        </el-input>
                                    </template>
                                </el-table-column>

                                <el-table-column label="汇报日期" width="190" align="center">
                                    <template slot-scope="scope">
                                        <el-date-picker type="date" placeholder="选择时间" :style="{width: '130px'}"
                                            v-model="scope.row.hbrq" value-format="yyyy-MM-dd">
                                        </el-date-picker>
                                    </template>
                                </el-table-column>
                                <el-table-column label="汇报附件" align="center" width="300px">
                                    <template slot-scope="scope">
                                        <uploadFile 
                                            :type="'file'"
                                            :limit="'3'"
                                            :btnMsg="'点击上传'"
                                            :ywid="scope.row.hbfj"
                                            :fjlx="'SXHB'"
                                            :xtmk="'SXHB'"
                                            v-if="scope.row.hbfj != '' "
                                            :isView="false"
                                        ></uploadFile>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-col>
                    </el-row>
                </fieldset>

                <div class="button_div">
                    <el-form-item size="large" align="center">
                        <el-button type="primary" @click="saveData">保存</el-button>
                        <el-button type="success" @click="submitForm">提交</el-button>
                    </el-form-item>
                </div>

            </el-form>
        </div>

        <!-- 以下是简单弹出列表页 -->
        <el-dialog  v-dialogDrag 
            v-if="popListFormVisable"
            title="简单弹出页" 
            width="980px"

            :visible.sync="popListFormVisable" 
            :close-on-click-modal="false" >
                <popListForm 
                    :id="actId" 
                    :newId="newId" 
                    :operation="operation" 
                    @confirmSelect="handlePopListSelect" 
                    @clearSelect="handlePopListClear" 
                    
                ></popListForm>

        </el-dialog>

        <!-- 以下是简单弹出穿梭框页 -->
        <el-dialog  v-dialogDrag 
            v-if="popTransferFormVisable"
            title="简单弹出多选" 
            width="680px"

            :visible.sync="popTransferFormVisable" 
            :close-on-click-modal="false" >
                <popTransferForm 
                    @confirmSelect="handlePopTransferSelect" 
                    @clearSelect="handlePopTransferClear" 
                    
                ></popTransferForm>

        </el-dialog>

        <!-- 以下是弹出多选列表页 -->
        <el-dialog  v-dialogDrag 
            v-if="popWlxxFormVisable"
            title="弹出多选列表" 
            width="1180px"
            :visible.sync="popWlxxFormVisable" 
            :close-on-click-modal="false" >
                <popWlxxForm 
                    @confirmSelect="handlePopWlxxSelect" 
                    @clearSelect="handlePopWlxxClear" 
                    
                ></popWlxxForm>

        </el-dialog>

    </div>
</template>
<script>
import popListForm from "./zhyy-pop";
import popWlxxForm from "./zhyy-pop-multi";
import popTransferForm from "./zhyy-transfer";
import uploadFile from "./zhyy-upload-file";

import { v4 as getUUID } from 'uuid'

export default {
    name: "zhyy-edit-form",

    components: {
        popListForm,
        popWlxxForm,
        popTransferForm,
        uploadFile,

    },
   
    data() {
        return {
            id : this.$route.params.id,

            sxhbList: [],
            currentRow: null,

            RQQJ:['2021-01-01', '2021-01-02'],
            formData: {
                SRK: undefined,
                XLK: 'QUERY',
                RQK: undefined,
                SZK: undefined,
                TCXZ: undefined,
                JDDX: undefined,
                TCDX: undefined,
                DXK: undefined,
                FXK: undefined,
                WBQY: undefined,
                RQQJ: undefined,
                XLS_PATH: undefined,
                FJSC: '1',
                CON: undefined,
                CON_IF: undefined,
                ID: undefined,
            },
            con_if_show : false,
            rules: {
                SRK: [{
                    required: true,
                    message: '输入框内容不得为空',
                    trigger: 'blur'
                }],
                XLK: [{
                    required: true,
                    message: '下拉框不得为空',
                    trigger: 'blur'
                }],
                RQK: [{
                    required: true,
                    message: '日期框不得为空',
                    trigger: 'blur'
                }],
                SZK: [{
                    required: true,
                    message: '数字框不得为空',
                    trigger: 'blur'
                }],
                SQL: [{
                    required: true,
                    message: '请输入SQL',
                    trigger: 'blur'
                }],
                ARGIN: [],
                CONIN: [],
                
            },
            LBOptions: [{
                "label": "查询",
                "value": "QUERY"
            }, {
                "label": "执行",
                "value": "EXECUTE"
            }],

            xlTreeList: [{id:'1', text:'选项1', children:[{id:'11', text:'选项11'},{id:'12', text:'选项12'}]},{id:'2', text:'选项2'}],
            xlProps: {
                value: 'id',
                label: 'text',
                children: 'children', 
            },

            popListFormVisable: false,
            popWlxxFormVisable: false,
            popTransferFormVisable: false,




        }
    },
    props: {
        operation: {
            type: String,
            default: ""
        },
        newId: {
            type: String,
            default: ""
        },
    },
    mounted() {

    },
    methods: {
        resetForm() {
            this.$refs['dataForm'].resetFields()
        },
        closeForm() {
            this.$emit('handleCloseViewDialog');
        },

        async saveData() {
            var isValid = false;
            await this.$refs.dataForm.validate(valid => {
                isValid = valid;
            });
            if(!isValid) {
                this.$alert('页面有必填项未填写！', '提示', {
                    type: 'error'
                });
                return;
            }
            RESTAPI.saveDataFormConfigData(this.formData,
                response => {
                    this.listLoading = false;
                    if (
                        response.data &&
                        response.data.result === 20000 &&
                        response.data.data
                    ) {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000,
                            offset: 80,
                        });

                        // 添加成功后返回调用钩子函数，关闭弹窗 
                        this.$emit('handleSubmitSuccess');
                    } else {
                        this.$notify({
                            title: "警告",
                            message: "保存失败-401",
                            type: "warning",
                            duration: 2000,
                            offset: 80
                        });
                    }
                },
                error => {
                    // this.listLoading = false;
                    this.$notify({
                        title: "警告",
                        message: "保存失败-402" + error,
                        type: "warning",
                        duration: 2000,
                        offset: 80
                    });
                }
            );

        },

        submitForm() {
            this.$confirm("确定要提交吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                }).then(() => {
                    this.saveData();

                }
            ).catch(() => {});

        },

        /**
		 * @description: 打开简单的弹出选择页
		 * @param {*}
		 * @return {*}
		 */
         popListSelect() {
            this.popListFormVisable = true;
        },
        /**
		 * @description: 关闭简单的弹出选择页
		 * @param {*}
		 * @return {*}
		 */
        handleClosepopListFormDialog(done) {
            this.popListFormVisable = false;
        },
        /**
		 * @description: 确认
		 * @param {*}
		 * @return {*}
		 */
         handlePopListSelect(rowData) {
            console.log(rowData);
            this.formData.TCXZ = rowData[0].NAME

            this.popListFormVisable = false;

        },
        /**
		 * @description: 清空
		 * @param {*}
		 * @return {*}
		 */
         handlePopListClear() {
            this.formData.TCXZ = '';
            this.popListFormVisable = false;

        },



        /**
		 * @description: 打开简单的弹出穿梭页
		 * @param {*}
		 * @return {*}
		 */
         popTransferSelect() {
            this.popTransferFormVisable = true;
        },
        
        /**
		 * @description: 在弹出穿梭页点击确认
		 * @param {*}
		 * @return {*}
		 */
         handlePopTransferSelect(rowData) {
            console.log(rowData);
            this.formData.JDDX = rowData.join(',')
            this.popTransferFormVisable = false;

        },
        /**
		 * @description: 在弹出穿梭页点击清空
		 * @param {*}
		 * @return {*}
		 */
         handlePopTransferClear() {
            this.formData.JDDX = '';
            this.popTransferFormVisable = false;

        },



        /**
		 * @description: 打开复杂多选列表
		 * @param {*}
		 * @return {*}
		 */
         popWlxxSelect() {
            this.popWlxxFormVisable = true;
        },
        
        /**
		 * @description: 在复杂多选列表页点击确认
		 * @param {*}
		 * @return {*}
		 */
         handlePopWlxxSelect(rows) {
            var nameArr = [];
            for(var i = 0; i < rows.length; i++){
                nameArr.push(rows[i].NAME);

            }
            this.formData.TCDX = nameArr.join(',')
            this.popWlxxFormVisable = false;

        },
        /**
		 * @description: 在复杂多选列表页点击清空
		 * @param {*}
		 * @return {*}
		 */
         handlePopWlxxClear() {
            this.formData.TCDX = '';
            this.popWlxxFormVisable = false;

        },


        addHb() {
			let pxh = this.sxhbList.length + 1;
			var newSxhbid = getUUID().replace(/-/g, '').toUpperCase();
			var newHbfj = getUUID().replace(/-/g, '').toUpperCase();
			let hbData = {
				sxhbid: newSxhbid,
				pxh: pxh,
				applicantid : this.applicantId,
				activityCode : this.activityCode,
				hbrq: "",
				hbnr: "",
				hbfj: newHbfj,
			}
			this.sxhbList.push(hbData);
        },
        handleCurrentChange(val) {
			this.currentRow = val;
        },
        orderXH(d) {
			for (var i = 0; i < d.length; i++) {
				d[i].pxh = i + 1;
			}
		},
        deleteHb(val) {
            if(!this.currentRow){
                this.$alert('请先选择要删除的行！', '提示', {
                    type: 'warning'
                });
                return;
            }

            this.$confirm("确定删除？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
            .then(() => {
                let index = this.currentRow.pxh - 1;
                this.sxhbList.splice(index, 1);
                this.orderXH(this.sxhbList);
            
            })
            .catch(err => {
            });


			
		},

        goBack(){
            this.$router.push({path:'/demo/queryList2', query:{}});

        },

    }
}
</script>

<style >
.el-input-number {
  text-align: left;
}
</style>