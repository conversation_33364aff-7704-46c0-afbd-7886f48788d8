@media (max-width:700px){
  .app .wrapper .header-box .top-navs
  {
    display: none;
  }
  .app .wrapper .header-box .header .header-right {
      display: none;
  }
  .app .wrapper .header-box .header .logo
  {
    background: url(../../../img/logo1.png) ;
    background-position: center;
    background-repeat: no-repeat;
    background-origin: content-box;
    background-size: 90px 40px;
    background-clip: content-box;
  }
  .app .wrapper .header-box .left-navs{
    width:200px;
    height: -moz-calc(100% - 90px);/*减顶部50px和底部40px*/
    height: -webkit-calc(100% - 90px);
    height: calc(100% - 90px);
  }
  
  .app .wrapper .content-box .content .el-container-scoped .el-aside-scoped
  {
    display: none;
  }


  .app .wrapper .content-box {
    height: -moz-calc(100% - 90px);/*减顶部50px和底部40px*/
    height: -webkit-calc(100% - 90px);
    height: calc(100% - 90px);

  }

  .app .wrapper .footer-box .footer{
    display: none;
  }

  .app .wrapper .footer-box .bottom-navs{
    height: 40px;
    display: block;
  }

}
  

  
  /*********************************以下为框架整体布局样式控制，工作区布局请自行在页面中定义自己的样式********************************************/

  
