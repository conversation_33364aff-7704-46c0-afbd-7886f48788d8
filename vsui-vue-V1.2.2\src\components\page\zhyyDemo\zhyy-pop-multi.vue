<template>
  <div >
    <el-row class="zhyy-list-searchAreaPop" >
      <el-col :span="18">
          <label >物料描述：</label>
          <el-input @keyup.enter.native="processSearch" style="width: 200px;"
              class="filter-item" placeholder="物料描述" v-model="listQuery.name" >
          </el-input>

          <el-button type="primary" icon="el-icon-search" style="margin-left:8px;"
              @click="processSearch">查询</el-button>


      </el-col>
      <el-col :span="6" style="text-align:right">
          <el-button type="success" icon="el-icon-check" @click="confirmSelect">确定</el-button>
          <el-button type="danger" icon="el-icon-delete" @click="clearSelect">清空</el-button>

      </el-col>
    </el-row>
  
    <!--数据表格-->
    <el-row>
      <el-col :span="12" >
        <el-table
          :stripe="true" 
          :data="tableData"
          ref="accountTable"
          width="50%"
          :header-cell-style="{ background: '#F4F7FA'}"
          highlight-current-row
          @current-change="handleLeftTableChange"
        >
          <el-table-column type="index" width="50" prop="order" header-align="center" align="center" label="序号" ></el-table-column>
          <el-table-column prop="NAME" label="待选物料描述" width="260" header-align="center" align="left"></el-table-column>
          <el-table-column prop="WLBM" label="待选物料编码" width="186" header-align="center" align="center"></el-table-column>
          <el-table-column
            property="CZ"
            label="操作"
            width="70"
            header-align="center"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                @click="addWlxx(scope.row)"
                type="text"
                >添加</el-button>
            </template>
          </el-table-column>

                      
        </el-table>
        <div class="zhyy-list-paginationArea">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="listQuery.pageIndex" :page-sizes="[10,20,30,50]" :page-size="listQuery.pageSize"
              layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
        </div>
      </el-col>
      <el-col :span="1" >&nbsp;</el-col>
      <el-col :span="11" >
        <el-table
          :data="tableData2"
          ref="accountTable2"
          border
          width="50%"
          height="calc(100vh - 440px)"
          :header-cell-style="{ background: '#F4F7FA'}"
          highlight-current-row
          @current-change="handleLeftTableChange"
        >
          <el-table-column type="index" width="50" prop="order" header-align="center" align="center" label="序号" ></el-table-column>
          <el-table-column prop="NAME" label="已选物料描述" width="200" header-align="center" align="left"></el-table-column>
          <el-table-column prop="WLBM" label="已选物料编码" width="176" header-align="center" align="center"></el-table-column>
          <el-table-column
            property="CZ"
            label="操作"
            width="70"
            header-align="center"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                @click="delWlxx(scope.row)"
                type="text"
                >删除</el-button>
                
            </template>
          </el-table-column>
        </el-table>
        
      </el-col>
    </el-row>
    <!--分页-->
    
  </div>
</template>
<script>

export default {
  name: "",
  components: {},
  props: {
    id: String,
  },
  //变量
  data() {
    return {
      
      tableData: [],
      tableData2: [],
      selectedData: undefined,
      listQuery: {
        pageflag: "yes",
        pageIndex: 1,
        pageSize: 10,
        wlms: "",
        wlbm: "",
      },
      total: 0,
    };
  },
  mounted: function () {

    this.loadData();

  },

  methods: {
    handleLeftTableChange(data) {
      this.selectedData = data;

    },
    
    addWlxx(rowData){
      for(var i = 0; i < this.tableData2.length; i++){
        if(rowData.WLBM == this.tableData2[i].WLBM){
          this.$message({message:'右侧已经存在！',type:'warning'});
          return;
        }
      }
      this.tableData2.push(rowData);

    },

    delWlxx(rowData){
      for(var i = 0; i < this.tableData2.length; i++){
        if(rowData.WLBM == this.tableData2[i].WLBM){
          this.tableData2.splice(i, 1);
          i--;
        }
      }
    },
    
    loadData() {
      this.listLoading = true;
      let param = Object.assign({}, this.listQuery, {
        formId: "selectWlxxList",
      });
      this.total = 10;
      var rows = [
                {
                    WLBM:'1',
                    NAME:'测试数据测试数据测试数据测试数据1',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    WLBM:'2',
                    NAME:'测试数据测试数据测试数据测试数据2',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                  WLBM:'3',
                    NAME:'测试数据测试数据测试数据测试数据3',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                  WLBM:'4',
                    NAME:'测试数据测试数据测试数据测试数据4',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },

            ];
      this.tableData = rows;
     
    },
    
    handleSizeChange(val) {
        this.listQuery.pageSize = val;
        this.loadData();
    },
    handleCurrentChange(val) {
        this.listQuery.pageIndex = val;
        this.loadData();
    },


    
    //确认
    confirmSelect() {
      if(this.tableData2.length == 0){
        this.$message({message:'请至少选择一行数据！',type:'warning'});
        return false;
      }
      
      this.$emit("confirmSelect", this.tableData2);
      
    },

    clearSelect(){
        this.$confirm("确定清空父页面数据？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        })
        .then(() => {
            this.$emit("clearSelect");
        })
        .catch(err => {
        });
        
    },



  },
  
};
</script>
<style>
</style>
<style scoped>
.el-table--striped .el-table__body tr.el-table__row--striped.current-row td, 
*/deep/.el-table__body tr.current-row>td {
	background-color: #c4e3fff6;
}
</style>