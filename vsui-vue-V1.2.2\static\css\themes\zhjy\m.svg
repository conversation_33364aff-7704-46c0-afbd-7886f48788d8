<svg id="指针_拷贝_2" data-name="指针 拷贝 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26" height="34" viewBox="0 0 26 34">
  <defs>
    <style>
      .cls-1 {
        stroke: #fff;
        stroke-width: 1px;
        fill-rule: evenodd;
        fill: url(#linear-gradient);
        filter: url(#filter);
      }

      .cls-2 {
        fill: #fff;
      }
    </style>
    <linearGradient id="linear-gradient" x1="552.094" y1="633.611" x2="541.031" y2="619.451" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0e2fd1"/>
      <stop offset="1" stop-color="#a9c4ff"/>
    </linearGradient>
    <filter id="filter" x="537" y="615" width="26" height="34" filterUnits="userSpaceOnUse">
      <feOffset result="offset" dx="2.536" dy="5.438" in="SourceAlpha"/>
      <feGaussianBlur result="blur" stdDeviation="2.646"/>
      <feFlood result="flood" flood-color="#0e2fd1" flood-opacity="0.3"/>
      <feComposite result="composite" operator="in" in2="blur"/>
      <feBlend result="blend" in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(-537 -615)" style="fill: url(#linear-gradient); filter: url(#filter)">
    <path id="形状_1" data-name="形状 1" class="cls-1" d="M541.034,617.3v14.949a3.077,3.077,0,0,0,2.408,3.124,10.735,10.735,0,0,0,5.471,0,9.366,9.366,0,0,0,2.845-4.016,3.615,3.615,0,0,0-.875-4.24C548.865,625.09,541.034,617.3,541.034,617.3Z" style="stroke: inherit; filter: none; fill: inherit"/>
  </g>
  <use transform="translate(-537 -615)" xlink:href="#形状_1" style="stroke: #fff; filter: none; fill: none"/>
  <circle id="椭圆_1" data-name="椭圆 1" class="cls-2" cx="9.813" cy="15.297" r="1.532"/>
</svg>
