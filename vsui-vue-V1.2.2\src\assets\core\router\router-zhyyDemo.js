/*
 * @Description: 综合应用demo
 * @version: v1.0.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-04-13 22:29:15
 * @LastEditors: cheng<PERSON><PERSON>
 * @LastEditTime: 2023-04-22 12:44:38
 */
function checkLogined(to,userName,userPermission){
  //获取用户名可以使用如下方法：
  //方式1：本函数参数会传入userName
  //方式2：VSAuth.getUserName();
  return userName!=""
}

const routerZhyyDemo = [
  {
    path: "dashboard",
    name: "dashboard",
    component:()=> import("../components/page/Dashboard.vue"),

    meta: { title: "系统首页", permission: checkLogined }
  },
  {
    path: "queryList",
    name: "queryList",
    component: ()=> import("../components/page/zhyyDemo/zhyy-search-simple.vue"),
    meta: {
      title: "简单列表与表单", permission: checkLogined
    }
  },
  {
    path: "form",
    name: "form",
    component: ()=> import("../components/page/zhyyDemo/zhyy-edit-form.vue"),
    meta: {
      title: "综合部-简单查询", permission: checkLogined
    }
  },
  {
    path: "form",
    name: "form",
    component: ()=> import("../components/page/zhyyDemo/zhyy-edit-dialog"),
    meta: {
      title: "综合部-简单查询", permission: checkLogined
    }
  },
  {
    path: "zhyyEditForm",
    name: "zhyyEditForm",
    component: ()=> import("../components/page/zhyyDemo/zhyy-edit-form.vue"),
    meta: {
      title: "综合部-简单表单", permission: checkLogined
    }
  },


  {
    path: "queryList2",
    name: "queryList2",
    component: ()=> import("../components/page/zhyyDemo/zhyy-search-complex.vue"),
    meta: {
      title: "复杂列表页", permission: checkLogined
    }
  },

  {
    path: "queryTreeList",
    name: "queryTreeList",
    component: ()=> import("../components/page/zhyyDemo/zhyy-search-haveLeft.vue"),
    meta: {
      title: "左侧树右侧列表", permission: checkLogined
    }
  },
  {
    path: "queryEditList",
    name: "queryEditList",
    component: ()=> import("../components/page/zhyyDemo/zhyy_lineedit_list.vue"),
    meta: {
      title: "行编辑列表", permission: checkLogined
    }
  },

  {
    path: "imgView",
    name: "imgView",
    component: ()=> import("../components/page/zhyyDemo/imgView.vue"),
    meta: {
      title: "原型设计", permission: checkLogined
    }
  },

  {
    path: "fwbEdit",
    name: "fwbEdit",
    component: ()=> import("../components/page/zhyyDemo/zhyy-fwbEdit.vue"),
    meta: {
      title: "富文本编辑", permission: checkLogined
    }
  },

  ]

  export default routerZhyyDemo;
