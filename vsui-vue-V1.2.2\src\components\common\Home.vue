<template>
  <div class="wrapper">
    <v-header v-if="!hiddenHeader"></v-header>
    <div class="content-box" :class="{'content-collapse':collapse,'content-box-hiddenHeader':hiddenHeader}">
      <div class="content">
        <transition name="move" mode="out-in">
          
            
            <router-view></router-view>
            
          
        </transition>
      </div>
    </div>
    <v-footer v-if="!hiddenHeader"></v-footer>
  </div>
</template>

<script>
import vHeader from "./Header.vue";
import vFooter from "./Footer.vue";
import {runtimeCfg} from "../../assets/core/"
export default {
  data() {
    return {
      tagsList: [],
      collapse: false,
      hiddenHeader:this.$route.query.hasOwnProperty("hidtop")?true:runtimeCfg.page_hid_topbottom,
    };
  },
  components: {
    vHeader,
    vFooter,
  },
  created() {
 
  },
  mounted() {
    let _this=this;
    document.addEventListener("click", function (e) {
      _this.$EventBus.$emit("documentclick",e);
    });
  }
};
</script>
