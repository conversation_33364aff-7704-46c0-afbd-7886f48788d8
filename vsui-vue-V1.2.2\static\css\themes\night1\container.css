@import url("vue-components-drag-panel.css");
@import url("vue-components-page-loader.css");
@import url("about-me-index.css");



.app .wrapper .content-box .content .el-card-scoped .el-card__body,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__body
{
    background-color: transparent;
}

.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container , 
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__body .el-container 
{
    background-color: transparent;
    overflow: auto;
}

.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main-scoped,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__body .el-container .el-main-scoped 
{
    padding: 10px;
    background-color: transparent;
    opacity: 0.9;
    border-radius: 5px;
    color:#fff;
    overflow: auto;
}

.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__body .el-container .pager  .el-pagination-scoped .el-pagination__total
 {
     color:#fff;
 }
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__body .el-container .pager  .el-pagination-scoped .el-pagination__jump
 {
    color:#fff;
 }