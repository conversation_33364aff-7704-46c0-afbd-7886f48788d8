<template>
    <div class="qlwz-list-container">
        <div class="qlwz-list-main">
            <div class="qlwz-list-searchArea">
                <h3 v-if="type == 'dzbsjsh'" style="text-align: center;">纪检委员待审</h3>
                <h3 v-if="type == 'jjbsh'" style="text-align: center;">纪检部待审</h3>
                <el-row>
                    <el-col :span="18">
                        <label>标题:</label>
                        <el-input @keyup.enter.native="processSearch" style="width: 200px;" class="filter-item"
                            placeholder="标题" v-model="listQuery.infoTitle" clearable>
                        </el-input>
                        <el-button type="primary" icon="el-icon-search" style="margin-left:8px;"
                            @click="processSearch">查询</el-button>
                    </el-col>
                </el-row>
            </div>
            <el-row class="qlwz-list-tableArea">
                <el-table border v-loading="listLoading" :data="tableData"
                    :header-cell-style="{ background: '#F4F7FA' }">
                    <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px">
                    </el-table-column>
                    <el-table-column min-width="20%" align="left" header-align="center" label="标题" prop="infoTitle" show-overflow-tooltip>

                    </el-table-column>
                    <el-table-column min-width="10%" align="center" header-align="center" label="发布时间"
                        prop="publishTime">

                    </el-table-column>
                    <el-table-column min-width="10%" align="center" header-align="center" label="上报单位"
                        prop="operatororgName">

                    </el-table-column>

                    <el-table-column min-width="10%" label="操作" align="center" header-align="center">
                        <template slot-scope="scope">
                            <el-button size="mini" type="text" @click="editData(scope.row)">【审核】</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="qlwz-list-paginationArea">
                    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :page-sizes="[10, 20, 30, 50]" :page-size="listQuery.pageSize"
                        :current-page="listQuery.pageIndex" layout="total, sizes, prev, pager, next, jumper"
                        :total="total"></el-pagination>
                </div>
            </el-row>

        </div>
        <!-- 以下是新增信息的弹出页 -->
        <el-dialog v-dialogDrag v-if="shDialog" width="52%" :title="shTitle" :visible.sync="shDialog"
            :before-close="handleCloseViewDialog" :close-on-click-modal="false">
            <dzbsjds :rowData="rowData" :type="type" @handleSubmitSuccess="handleSubmitSuccess"
                @handleCloseViewDialog="handleCloseViewDialog" />
        </el-dialog>
    </div>
</template>
<script>
import dzbsjds from './ds.vue'
import VSAuth from "@vsui/lib-vueauth4vseaf";
export default {
    components: {
        dzbsjds
    },
    props: {
        type: {
            type: String,
            default: ""
        },
    },
    data() {
        return {
            rowData: {},
            total: null,
            tableData: [],
            listLoading: true,
            listQuery: {
                pageIndex: 1,
                pageSize: 10,
                infoTitle: '',
                processStatus: '',
                infoStatus: '',
                isAudit: '1',
                userId: VSAuth.getAuthInfo().permission.userId

            },
            //审核信息的变量
            shTitle: '',
            shDialog: false,
        }
    },
    watch: {
        $route: {
            handler(oldVal) {
                this.loadListData();
            },
            deep: true
        },
    },
    mounted() {
        this.loadListData();
    },
    methods: {
        //加载数据列表
        loadListData() {
            if (this.type == 'dzbsjsh') {
                this.listQuery.processStatus = '1';
            } else {
                this.listQuery.processStatus = '2';
            }

            this.axios.post('/backend/qlwz/queryInfoDataList', this.listQuery
            ).then(resp => {
                if (resp.data.data) {
                    this.tableData = resp.data.data.rows;
                    this.total = resp.data.data.total;
                    this.listLoading = false;
                }
            }).catch((error) => {
                this.$message.error('查询失败')
                this.listLoading = false;
            });

        },
        //审核
        editData(row) {
            if (this.type == 'dzbsjsh') {
                this.shTitle = '纪检委员待审';
            } else {
                this.shTitle = '纪检部待审';
            }

            this.shDialog = true;
            this.rowData = row;
        },

        // 弹出页面点击关闭按钮回调函数
        handleCloseViewDialog() {
            this.shDialog = false;
            this.loadListData();
        },
        // 弹出页面提交后回调函数

        handleSubmitSuccess() {
            this.shDialog = false;
            this.loadListData();
        },

        //查询数据
        processSearch() {
            this.listQuery.pageIndex = 1;
            this.loadListData();
        },
        // 变更每页条数时自动加载数据
        handleSizeChange(val) {
            this.listQuery.pageSize = val;
            this.loadListData();
        },

        // 点击某一页时自动加载数据
        handleCurrentChange(val) {
            this.listQuery.pageIndex = val;
            this.loadListData();
        },

    }
}
</script>
<style scoped></style>