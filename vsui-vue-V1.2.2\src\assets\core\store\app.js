
export default
{
    strict: process.env.NODE_ENV !== 'production',
    /**
     * getters访问：getters['APP/isAdmin']
     * actions访问：dispatch('APP/login')
     * mutations访问：commit('APP/login')
     */
    namespaced: true,
    state:{
      /*
      访问状态请使用：
      import { mapGetters, mapState } from 'vuex'
      computed:{
        ...mapState({sysInfo:state=>state.APP.sysInfo})
      },*/
      configs:{},
      /**
       * 是否数据/页面加载中
       */
      Loading:false,

      /**
       * 皮肤名称
       */
      theme:"白蓝",

     
      
    },
    actions:{
      Loading:({commit},loading)=>{
        // console.log(`APP-Store内部接收到actions[Loading]的调用,值为：`+loading)
        commit("Loading",loading);
      }

    },
    mutations:{

      initSysConfig:function(state,configs)
      {
        state.configs=configs;
      },

     
      /**
       * 设置主题
       * @param {*} state 
       * @param {*} theme 主题名称
       */
      setTheme:function(state,theme)
      {
        state.theme=theme;
      },
      
      
      Loading:function(state,loading)
      {

        if(!state.loadingCount){state.loadingCount=0}
        if(loading){
          state.loadingCount++;
          state.Loading=true;
        }
        else{
          state.loadingCount<=0?state.loadingCount=0:state.loadingCount--;
          if(state.loadingCount==0)state.Loading=false;
        }
        
      }

    },
    getters:
    {
      getConfig:(state)=>{return state.configs},
      

      /**
       * 获取主题
       */
      getTheme:state=>{return state.theme},

      
           
    }
  }