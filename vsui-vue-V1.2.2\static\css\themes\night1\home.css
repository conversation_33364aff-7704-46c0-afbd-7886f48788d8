@import url("header.css");
@import url("footer.css");
@import url("dashboard.css");
@import url("sidebar.css");
@import url("container.css");

/**************************主内容区域***********************************/

.app .wrapper .content-box {
    background: transparent;
  }
  .app .wrapper .content-box-hiddenHeader {
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition:all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    background: #f0f0f0;
  }
  
  .app .wrapper .content-box.hidestate
  {
    transition: all 0.2s ease-in-out;
  }
  .app .wrapper .content-box .content {
    background-color: transparent ;
  }
  
  .app .wrapper .content-box .content .el-container-scoped {
    background: transparent;
  }
  
  /* 左侧导航样式 */
  .app .wrapper .content-box .content .el-container-scoped .el-aside-scoped {
    
    background-color: transparent;
    color: #fff;
  }
  
  /* 内容区 背景色 */
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped-load {
  
  }
  
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped {
 
  }
  
  .app .wrapper .content-box .content .el-card-scoped,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped {
    background-color: transparent;
  }

  .app .wrapper .content-box .content .el-card-scoped .el-card__header,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__header {
    border-bottom: 1px solid #666 ;
  }
  
  /*默认头部标题字体样式 */
  .app .wrapper .content-box .content .el-card-scoped .el-card__header .title_bar,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__header .title_bar{
    color: #fff;
    font-weight: normal;
  }
  .app .wrapper .content-box .content .el-card-scoped .el-card__header .el-button-scoped,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__header .el-button-scoped{
    
  }
  



  