 /***
 *  @author:cuiliang 
 *  @email: <EMAIL>
 *  @date：20210323
 *  @version:V1.2.0
 *  @description:
 *  该模块全局一次引入，
 *  软件中如有对该模块方法调用时，请将该模块在main.js中引入，即可在程序运行时就完成了对基础类型的扩展，
 *  如对此机制不明白的请查阅javascript相关文档
 *  对ElementUI的引用与配置
 *  @link  ElementUI使用教程请见：http://element-cn.eleme.io/#/zh-CN
 * 
 * 
 * 
 */ 

import Vue from 'vue';
import ElementUI from 'element-ui';
import locale from 'element-ui/lib/locale/lang/zh-CN';
import 'element-ui/lib/theme-chalk/index.css';// 默认主题



Vue.use(ElementUI,
    {
        locale, 
        size: "small" 
})

export default ElementUI;