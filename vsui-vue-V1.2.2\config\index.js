'use strict'

var path = require('path')
var proxyTable=require("./dev.proxy.js")


module.exports = {
  dev: {
    
    assetsSubDirectory: 'static',
    proxyTable:proxyTable,
    // Various Dev Server settings
    host: 'localhost', // can be overwritten by process.env.HOST
    //host: "appmonitor.slof.com",//SIAM
    //端口占用释放使用以下两条命令
    //查找端口占用的PID netstat -aon|findstr "8088"
    //使用PID杀死进程 taskkill -pid {PID} /F
    port: 8088, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    //port: 80,
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    
    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'eval-source-map',

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    // CSS Sourcemaps off by default because relative paths are "buggy"
    // with this option, according to the CSS-Loader README
    // (https://github.com/webpack/css-loader#sourcemaps)
    // In our experience, they generally work as expected,
    // just be aware of this issue when enabling this option.
    cssSourceMap: false,

  },

  build: {
    // Template for index.html
    /*webAppTitle: publishConfig.title,
    webAppKeyWords: publishConfig.keyWords,
    webAppDescription: publishConfig.description,*/

    index: path.resolve(__dirname, '../dist/index.html'),
    // Paths
    assetsRoot: path.resolve(__dirname, '../dist/'),
    assetsSubDirectory: 'static',
    assetsApiDataDirectory: 'apidata',
    //assetsPublicPath: publishConfig.publicRootPath,
    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: 'eval-source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report,

    

  },

  
}
