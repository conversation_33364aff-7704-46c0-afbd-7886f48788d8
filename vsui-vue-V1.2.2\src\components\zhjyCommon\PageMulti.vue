<template>

    <el-container class="el-container-scoped">
       <!-- 左侧 -->
      <div :class="leftFrameClass">
            <SidebarPage v-show="hiddenSidebar" :SidebarFlag="SidebarFlag" :modules="modulesTree==null?[]:modulesTree" @beforemoduleclick="beforemoduleclick" @moduleclicked="moduleclicked"></SidebarPage>
      </div>
      <!-- 右侧 -->
      <div :class="rightFrameClass">
        <v-header v-show="hiddenHeader!=''" :cutTabTitle="cutTabTitle" @leftShowHide="leftShowHide"></v-header>
      
        <el-main ref="workbench"  class="el-main-scoped">
          <el-tabs v-model="currentTab"  tab-position="top" type="card" @tab-click="handleTabClick" @edit="handleTabEdit" class="el-tabs-scoped">
              <el-tab-pane
                  v-for="(item, index) in pageMultiTabs"
                  :key="item.name"
                  :label="item.title"
                  :name="item.name"
                  :closable="item.closable"
                >
                  <span v-if="typeof(item.content) == 'string'">{{item.content}}</span>
                  <component v-bind="item.props" v-else-if="typeof(item.content) == 'function'||typeof(item.content)=='object'" :key="item.title" :is="item.content" ></component>
                </el-tab-pane>
          </el-tabs>
        </el-main>
      </div>
    </el-container>  
</template>
<script>
import SidebarPage from "./Sidebar.vue";
import hash from "../../lib/hash";
import VSAuth from "@src/lib/vsAuth";
import vHeader from "./Header.vue";
import {mixin,runtimeCfg} from "../../assets/core/index";

export default {
  components: {
    SidebarPage,
    vHeader
  },
  mixins:[mixin],
  props:{
    indexPath:{
      type:String,
      default:"",
    }
  },
  data() {
    return {
      hiddenSidebar: true,
      pageMultiTabs: [],
      currentTab:hash(this.indexPath),
      cutTabTitle:'',
      SidebarFlag :false,
      leftFrameClass:'frame_left', // 左侧class
      rightFrameClass:'frame_right', // 右侧class
    };
  },
  computed: {
    modulesTree : function(){return this.auth.instance.getModulesTree()},
  },
  methods:{

    /**
     * 左侧菜单收起方法
     * true 为显示 false  为隐藏
     */
    leftShowHide(treeShow){

      //显示
      if(treeShow){
        this.leftFrameClass = 'frame_left';
        this.rightFrameClass = 'frame_right';
      }else{//收起
        this.leftFrameClass = 'frame_left_hide';
        this.rightFrameClass = 'frame_right_hide';
      }

      this.SidebarFlag = !treeShow;
    },

    /**
     * 侧边栏点击后处理事件，
     * 
     */
    moduleclicked(routePath,closable)
    {
      //获取路由定义信息
      let routeDef=this.$router.match(routePath);
      //根据路由获取组件定义列表
      //let componentDefs=this.$router.getMatchedComponents(routePath);
      //根据路由获取最后一个组件定义，即当前路由地址对应的最终组件
      //let componentDef=componentDefs[componentDefs.length-1];
      //获取组件默认构造
      let componentInfo=routeDef.matched[routeDef.matched.length-1].components.default;
      //获取组件默认构造参数props
      let componentProps=routeDef.matched[routeDef.matched.length-1].props.default;
      componentProps= (componentProps===true?componentProps:
      (typeof(componentProps)=="object"?componentProps:
      (typeof(componentProps)=="function"?componentProps(this.$route):"")))

      let module={};
      for(let resItem of this.auth.instance.getPermission().resList.values())
      {
        if(resItem.resPvalue==routePath)
        {
          module=resItem;
          break;
        }
      }
      //赋值head标题
      this.cutTabTitle = routeDef.meta.title;

      //新增选项卡
      this.addTab(routeDef.meta.title,
      routePath,
      componentInfo,
      componentProps,
      closable)
    },
    /**
     * 添加tab选项卡
     * @param string title  tab选项卡文字
     * @param string name  tab唯一识别
     * @param vue content  tab-content所要加载的vue组件对象
     * @param json props  要加载的vue对象的参数
     */
    addTab(title,name,content,props,closable)
    {
      name=hash(name);
      
      for(let tabItem of this.pageMultiTabs.values())
      {
        if(tabItem.name==name) {
          this.currentTab=tabItem.name;
          return;
        }
      }
      //判断标签页是否可关闭
      if(closable == null || closable == undefined){
        closable = true;
      }
      this.pageMultiTabs.push({
        title,
        "name":name,
        content,
        props,
        "closable":closable});
        this.currentTab=name;
    },
    handleTabClick(tab, event) {
      this.cutTabTitle = tab.label;
    },
    handleTabEdit(tabname, event) {
      if(event=="remove"){
        let removeIndex=-1;
        for(let i=0;i<this.pageMultiTabs.length;i++)
        {
          if(this.pageMultiTabs[i].name==tabname) {
            removeIndex=i;
            break;
          }
        }
        if(removeIndex>=0){
          this.pageMultiTabs.splice(removeIndex,1);
        }
        //关闭tab时展示左侧tab内容
        this.currentTab = this.pageMultiTabs[removeIndex-1].name;
      }
    },
  },
  mounted(){
    this.$EventBus.$on("moduleclicked", (routePath) => {
       this.moduleclicked(routePath);
    });

    console.log("----------");
    console.log(this.auth.instance.getModulesTree());
    console.log("----------");
    //this.moduleclicked('demo/queryList',false); 不允许删除页面
    // 允许删除页面
    this.moduleclicked(this.$route.path,true);
  }
};
</script>
  


<style scoped>
*>>>.el-aside{
    overflow: visible;
}

/* 取消左侧滚动条 */
::-webkit-scrollbar{
    width: 0px;
}
</style>