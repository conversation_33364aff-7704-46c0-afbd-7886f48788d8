<template>
    <div class="dialog_form">
        <el-form ref="formData" :model="formData" :rules="rules" size="small" label-width="100px">

            <el-form-item label-width="80px" label="标题" prop="infoTitle">
                <el-input v-model="formData.infoTitle" :style="{ width: '100%' }" :maxlength="100" show-word-limit
                    >
                </el-input>
            </el-form-item>
            <el-form-item label-width="80px" label="发布人" prop="operatorName">
                <el-input v-model="formData.operatorName" :disabled="true" :style="{ width: '100%' }" :maxlength="100"
                    show-word-limit>
                </el-input>
            </el-form-item>
            <el-form-item label-width="80px" label="发布时间" prop="publishTime">
                <el-date-picker v-model="formData.publishTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss" :disabled="true">
                </el-date-picker>
            </el-form-item>
            <el-form-item label-width="80px" label="主题图片" required="true">
                <vsfileupload class="uploadFile" v-if="showUpload" ref="upload" :busId="fileParams.busId"
                    :ywlb="formData.infoType + 'img'">
                </vsfileupload>

            </el-form-item>
            <el-form-item label-width="80px" label="主题视频" required="true">
                <vsfileupload class="uploadFile" v-if="showUpload" ref="upload" :busId="fileParams.busId"
                    :ywlb="formData.infoType + 'video'">
                </vsfileupload>

            </el-form-item>
            <el-form-item size="large" align="center" class="button_style">
                <el-button type="primary" @click="saveData()">保存</el-button>
                <el-button @click="closeForm()">关闭</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import vsfileupload from '../../../common/vsfileupload.vue'
import { v4 as getUUID } from 'uuid';
import VSAuth from "@vsui/lib-vueauth4vseaf";
export default {
    components: {
        vsfileupload,
    },
    props: {
        rowData: Object,//列表传入的行数据
    },
    data() {
        return {
            fileParams: {
                busId: '',//业务ID
                ywlb: '',//附件业务类型，区分同一数据多个文件类型
            },
            allowedPictureTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'], // 允许的图片类型
            allowedVideoTypes: ['video/mp4', 'video/avi'], // 允许的视频类型
            formData: {
            },
            rules: {
                infoTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],
                operatorName: [{ required: true, message: '请输入发布人', trigger: 'blur' }],
                publishTime: [{ required: true, message: '请选择发布时间', trigger: 'change' }],
            },
            showUpload: false,
        }
    },
    mounted() {
        this.getInfoData();
    },
    methods: {
        //保存信息
        saveData(zt) {
            var isValid = false;
            this.$refs.formData.validate((valid) => {
                isValid = valid;
            });
            if (!isValid) {
                this.$message({ message: '页面有必填项未填写', type: 'error' });
                return;
            }
            let fileList = this.$refs.upload.fileList;
            if (!fileList || fileList.length == 0) {
                this.$notify({ title: '警告', message: '请上传主题图片和视频', type: 'warning' });
                return;
            }
            this.axios({
                method: "post",
                url: "/backend/qlwz/saveInfoData",
                data: this.formData
            }).then(response => {
                if (response.data.data.status === 'success') {
                    this.$message.success("保存成功！");
                    this.closeForm();
                } else {
                    this.$message.error("保存失败！");
                }
            }).catch(error => {
                this.$message.error("保存失败！")
            })


        },
        //获取某条数据:根据infoId查询数据
        getInfoData() {
            this.axios({
                method: "post",
                url: "/backend/qlwz/queryInfoDataById",
                data: {
                    infoId: this.rowData.infoId,
                },
            }).then(resp => {
                if (resp.data.data && resp.data.data.infoId) {
                    this.formData = resp.data.data;
                    if (!resp.data.data.publishTime) {
                        this.formData.publishTime = this.utils.getDateTime();
                    }
                    this.fileParams.busId = this.formData.infoId;
                }
                this.showUpload = true;

            })
        },
        closeForm() {
            this.$emit('handleCloseViewDialog');
        },
    }


}
</script>
<style scoped>
.fupload {
    width: 125px;
    float: right;
}

.button_style {
    margin-left: -100px;
}
</style>