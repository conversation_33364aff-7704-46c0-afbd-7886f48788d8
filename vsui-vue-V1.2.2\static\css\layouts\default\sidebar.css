/**********************左侧菜单*****************************/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped{
  max-width: 230px;
  
  overflow: hidden;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs {
  overflow-y: hidden;
  overflow-x: hidden;
  width: 100%;
  height:100%;
  max-width: 229px;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold {
  height: 40px;
  line-height: 40px;
  box-sizing: border-box;
  -webkit-box-sizing:border-box;
  cursor: pointer;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold:hover{
}
/**和并展开小箭头**/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse{
  float: right;
  right: 20px;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  font-weight: 700;
  width: calc(50px - 1px);
  text-align: center;
  vertical-align: middle;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .left_navs_fold .collapse:hover{

}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .left_navs_fold .collapse>i {
  
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .left_navs_fold .collapse>i:hover {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .left_navs_fold .shortcuts{
  position: absolute;
  height: 40px;
  align-items: center;
  max-width: 180px;
  padding: 0 5px;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .left_navs_fold .shortcuts>div {

}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .left_navs_fold .shortcuts>div.shortcut {
  border: 1px solid #666;
  width:30px;
  height:30px;
  line-height: 30px;
  margin: 5px 0px 5px 5px;
  box-sizing: border-box;
  border-radius: 0px;
  text-align: center;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .left_navs_fold .shortcuts>div.plus {

  border: 1px dotted #666;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped {
  box-sizing: border-box;
  -webkit-box-sizing:border-box;
  height:calc(100% - 40px);
  overflow-x: hidden;
  overflow-y:auto ;
  border: 0px;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped[class~=el-menu--collapse] {
  overflow:hidden ;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-submenu__title {
   padding: 0 0 !important; 
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped.el-menu:not(.el-menu--collapse) {
  width: calc(230px - 1px); /*菜单展开宽度-1像素边框*/
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped.el-menu--collapse {
  width: calc(50px - 1px) ; /*菜单收起宽度-1像素边框*/
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-submenu__title:hover,
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu .is-opened .is-active,
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-scoped .is-active,
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-tree-node__content:hover {
}


.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped,
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped[class~=is-opened]{
  padding-left:0 !important;
  padding-right:0 !important;
  line-height: 50px ;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped[class~=el-submenu]{
  height:unset;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped:hover
{
}
/*********左侧菜单图标字体设置*************/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped i{
  font-size: 16px;
  font-weight: 700;
  width: 50px;
  text-align: center;
  vertical-align: middle;
  display: inline-block;
}


.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item.el-menu-item-scoped[class~=is-active]{
  font-weight: bold;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title{
  height: 50px;
  line-height: 50px;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title i{
  right: 10px;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover{
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover i {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu-item-title-scoped{
  margin-left: 0px!important;
  visibility: visible !important;
  height:unset ;
  width: unset ;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped {
}



.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content
{
  padding-left: 0px !important;
  height: 50px;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content>.el-tree-node__expand-icon
{
  padding:unset;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content:hover{
}


.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node[class*="is-current"] > .el-tree-node__content {
  font-weight: bold;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped i{
    width:20px;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped > .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped .custom-tree-node {
  display: inline-block;
  height: 50px;
  line-height: 50px;
  padding-right: 0;
}
/* 左侧导航样式 结束 */