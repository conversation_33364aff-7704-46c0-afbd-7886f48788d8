'use strict'
const utils = require('./utils')
const webpack = require('webpack')
const buildconfig = require('../config/index.js')
const merge = require('webpack-merge')
const chalk = require('chalk')
const baseWebpackConfig = require('./webpack.base.conf')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const FriendlyErrorsPlugin = require('friendly-errors-webpack-plugin')
const portfinder = require('portfinder')


let HOST = process.env.HOST || buildconfig.dev.host ;
let PORT = (process.env.PORT && Number(process.env.PORT))||buildconfig.dev.port;

const devWebpackConfig = merge(baseWebpackConfig, {
  mode:"development",
  entry: {
    app: ['./src/main.js']
  },
  module: {
    rules: utils.styleLoaders({ sourceMap: buildconfig.dev.cssSourceMap, usePostCSS: true })
  },
  // cheap-module-eval-source-map is faster for development
  devtool: buildconfig.dev.devtool,

  // these devServer options should be customized in /config/index.js
  devServer: 
  {
    clientLogLevel: 'warning',
    historyApiFallback: true,
    hot: true,
    compress: true,
    host: HOST,
    port: PORT,
    open: buildconfig.dev.autoOpenBrowser,
    overlay: buildconfig.dev.errorOverlay
      ? { warnings: false, errors: true }
      : false,
    publicPath: "/",
    proxy: buildconfig.dev.proxyTable,
    quiet: true, // necessary for FriendlyErrorsPlugin
    watchOptions: {
      poll: buildconfig.dev.poll,
    }
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env': require('../config/dev.env')
    }),
    new webpack.HotModuleReplacementPlugin(),
    new webpack.NamedModulesPlugin(), // HMR shows correct file names in console on update.
    new webpack.NoEmitOnErrorsPlugin(),
    // https://github.com/ampedandwired/html-webpack-plugin
    new HtmlWebpackPlugin({
      
      filename: 'index.html',
      template: 'index.html',
      inject: false,
      templateParameters: (compilation, assets, assetTags, options) => {
        return {
                  compilation,
                  webpackConfig: compilation.options,
                  htmlWebpackPlugin: {
                      tags: assetTags,
                      files: assets,
                      options,
                  },
                  prod:false,
              }
        },
    }),
  ]
})
const SUCCMSG=chalk.cyan(utils.sign+`

    [项目开发模式，如想运行框架自带示例请运行：npm run demo]

    最后编译时间：{date}

    应用程序运行在这里: ${chalk.white("http://{host}:{port}{path}")}`)

module.exports = new Promise((resolve, reject) => {
  portfinder.basePort = buildconfig.dev.port
  portfinder.getPort((err, port) => {
    if (err) {
      reject(err)
    } else {
      // publish the new Port, necessary for e2e tests
      process.env.PORT = port
      // add port to devServer config
      devWebpackConfig.devServer.port = port
      PORT=port;
      // Add FriendlyErrorsPlugin
      devWebpackConfig.plugins.push(new FriendlyErrorsPlugin({
        compilationSuccessInfo: {
          
          messages: [SUCCMSG.format({
            "date":(new Date()).format("yyyy-MM-dd hh:mm:ss"),
            "host":HOST,
            "port":PORT,
            "path":devWebpackConfig.devServer.publicPath
          })]
        },
        onErrors: buildconfig.dev.notifyOnErrors
        ? utils.createNotifierCallback()
        : undefined
      }))

      resolve(devWebpackConfig)
    }
  })
})
