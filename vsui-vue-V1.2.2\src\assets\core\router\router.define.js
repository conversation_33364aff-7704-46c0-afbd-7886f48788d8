
/**
 * 
 * 
 * 
 * 
 * 
 */
/* import routerZhyyDemo from "./router-zhyyDemo"; */
import routerQLWZDemo from "./router-qlwz";

import {
  customCheck,  //自定义验证过程，请打开router.auth.js找到此函数，实现函数体
  autoCheck,    //自动鉴权验证，鉴权过程首先验证用户是否登陆，然后验证前往的地址是否在用户可用的资源列表中
  withoutCheck  //无需验证，此情况下，路由定义中可以不存在meta.permission定义，也可以使用permission:withoutCheck ，
} from "./router.auth";

const routerDefine = [
    {
      path: "/login",
      name:"login",
      component: ()=> import('@views/Login.vue')
    },
    {
      path: "/404",
      name:"err_404",
      component: ()=> import("@views/404.vue")
    },
    {
      path: "/403",
      name:"err_403",
      component: ()=> import("@views/403.vue")
    },
    {
      path: "/",
      name: "root",
      redirect: "/qlwz/jdxs"
    },
    {
      path: "/",
      name: "home",
      component: ()=> import("@components/zhjyCommon/Home.vue"),
      meta: { title: "自述文件" },
      children: [
        // 监督专栏
        {
          path: "jdzlList",
          name: "jdzlList",
          props: { type: 'JDXS', },
          component: () => import("@components/page/qlwz/wzsy/infoWebList.vue"),
          meta: { title: "监督专栏"}
        },
        {
          path: "sjzlList",
          name: "sjzlList",
          props: { type: 'SJZL', },
          component: () => import("@components/page/qlwz/wzsy/infoWebList.vue"),
          meta: { title: "审计专栏"}
        },
        {
          path: "jddtList",
          name: "jddtList",
          props: { type: 'ZRQHD', },
          component: () => import("@components/page/qlwz/wzsy/infoWebList.vue"),
          meta: { title: "监督动态"}
        },
        {
          path: "infoWebView",
          name: "infoWebView",
          component: () => import("@components/page/qlwz/wzsy/infoWebView.vue"),
          meta: { title: "信息详情"}
        },
          {
            path: "wzsy",
            name: "wzsy",
            component: () => import("@components/page/qlwz/wzsy/show.vue"),
            meta: { title: "网站首页"}
        },
        {
            path: "detail",
            name: "detail",
            component: () => import("@components/page/qlwz/wzsy/detail.vue"),
            meta: { title: "网站视频详情页"}
        },
        {
          path: "videoList",
          name: "videoList",
          component: () => import("@components/page/qlwz/wzsy/videoList.vue"),
          meta: { title: "纪法课堂视频列表" }
        },
        {
          path: "dashboard",
          name: "dashboard",
          component: () => import("@components/page/Dashboard.vue"),

          meta: { title: "系统首页" }
        },
       /*  {
          path: "demo",
          name: "PageMulti",
          component: () => import("@components/zhjyCommon/PageMulti.vue"),
          meta: { title: "页面示例" },
          children: [
            ...routerZhyyDemo
          ]
        }, */
        {
          path: "/qlwz",
          name: "qlwzlist",
          component: () => import("@components/zhjyCommon/PageMulti.vue"),
          meta: { title: "清廉网站" },
          children: [
            ...routerQLWZDemo
          ]
        }
      ]
    },
    {
      path: "*",
      redirect: "/404"
    }
  ]

  export default routerDefine;