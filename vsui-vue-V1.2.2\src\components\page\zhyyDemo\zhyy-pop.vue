<template>
    <div class="zhyy-list-container">
        <div class="zhyy-pop-search-div">
            <el-row class="zhyy-list-searchAreaPop" >
                <el-col :span="18">
                    <label >表单名称：</label>
                    <el-input @keyup.enter.native="processSearch" style="width: 200px;"
                        class="filter-item" placeholder="表单名称" v-model="listQuery.name" >
                    </el-input>

                    <el-button type="primary" icon="el-icon-search" style="margin-left:8px;"
                        @click="processSearch">查询</el-button>


                </el-col>
                <el-col :span="6" style="text-align:right">
                    <el-button type="success" icon="el-icon-check" @click="confirmSelect">确定</el-button>
                    <el-button type="danger" icon="el-icon-delete" @click="clearSelect">清空</el-button>

                </el-col>
            </el-row>
        </div>
        
        <el-row class="zhyy-list-tableArea" >
            <el-table 
                :stripe="true"  
                v-loading="listLoading" 
                highlight-current-row
                ref="listTable"

                :data="tableData" 
                :row-key="getRowKey"
                :header-cell-style="{ background: '#F4F7FA'}"

                @row-click="onSelect"

                >
                
                <el-table-column type="selection" min-width="5%" align="center" :reserve-selection="true"></el-table-column>
                <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px">
                </el-table-column>
                <el-table-column min-width="20%" align="left" header-align="center" label="表单名称">

                    <template slot-scope="scope">
                        <span>{{scope.row.NAME}}</span>
                    </template>
                </el-table-column>
                <el-table-column min-width="10%" align="center" header-align="center"  label="表单类型">
                    <template slot-scope="scope">
                        <span>{{scope.row.LBMC}}</span>
                    </template>
                </el-table-column>
                <el-table-column min-width="10%" align="left" header-align="center" label="表单ID">
                    <template slot-scope="scope">
                        <span>{{scope.row.FORMID}}</span>
                    </template>
                </el-table-column>
                <el-table-column min-width="13%" align="left" header-align="center" label="返回值代码">
                    <template slot-scope="scope">
                        <span>{{scope.row.CODE}}</span>
                    </template>
                </el-table-column>
                <el-table-column min-width="17%" align="center" header-align="center" label="录入时间">
                    <template slot-scope="scope">
                        <span>{{scope.row.CRTIME}}</span>
                    </template>
                </el-table-column>
                
            </el-table>
            <div class="zhyy-list-paginationArea">
                <el-pagination background 
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" 
                    :page-sizes="[10, 20, 30, 50]" 
                    :page-size="listQuery.pageSize"
                    :current-page="listQuery.pageIndex"
                    layout="total, sizes, prev, pager, next, jumper" 
                    :total="total"
                    ></el-pagination>
            </div>
        </el-row>

    </div>
</template>

<script>
import { v4 as getUUID } from 'uuid'

export default {
    name: "index",
    
    components: {
    },

    data() {
        return {
            title: "表单工具列表",

            total: null,
            listLoading: true,
            tableData: undefined,
            pkColumn: 'ID',
            listQuery: {
                pageIndex: 1,
                pageSize: 10,
                name: "",
                id: "",
            },

            editFormTitle:'',

            actId: '',
            operation: '',
            newId: '',

        };
    },
    mounted() {
        this.loadListData();

    },
    methods: {
        /**
		 * @description: 加载列表数据
		 * @param {*}
		 * @return {*}
		 */
        loadListData() {
            this.listLoading = false;
            this.tableData = [
                {
                    ID:'1',
                    NAME:'选择数据1',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'2',
                    NAME:'选择数据2',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'3',
                    NAME:'选择数据3',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'4',
                    NAME:'选择数据4',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },

            ];

            this.total = 1;
        },

        /**
		 * @description: 查询数据，需要把页数重置为1
		 * @param {*}
		 * @return {*}
		 */
        processSearch() {
            this.listQuery.pageIndex = 1;
            this.loadListData();
        },

        /**
		 * @description: 变更每页条数时自动加载数据
		 * @param {*}
		 * @return {*}
		 */
        handleSizeChange(val) {
            this.listQuery.pageSize = val;
            this.loadListData();
        },
        
        /**
		 * @description: 点击某一页时自动加载数据
		 * @param {*}
		 * @return {*}
		 */
        handleCurrentChange(val) {
            this.listQuery.pageIndex = val;
            this.loadListData();
        },

        getRowKey(rowData){
            return rowData[this.pkColumn];
        }, 
        onSelect(val){
            //如果是单选
            var isSingleSelect = true;
            if(isSingleSelect){
                let currentSelect = this.$refs.listTable.selection;
                if(currentSelect.length > 0){
                    this.$refs.listTable.clearSelection();
                    if(currentSelect[0][this.pkColumn] != val[this.pkColumn]){
                        this.$refs.listTable.toggleRowSelection(val);
                    }
                }else{
                    this.$refs.listTable.toggleRowSelection(val);
                }
            }else{
                this.$refs.listTable.toggleRowSelection(val);

            }
            
        },

        confirmSelect(){
            let currentSelect = this.$refs.listTable.selection;
            if(currentSelect.length == 0){
                this.$notify({title: "提示", message: "请先选择行！", type: "info", duration: 2000, offset: 90});
                return;
            }

            this.$emit("confirmSelect", currentSelect);
        },

        clearSelect(){
            this.$confirm("确定清空父页面数据？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
            .then(() => {
                this.$emit("clearSelect");
            })
            .catch(err => {
            });
            
        },
        
    },
    
};

</script>


<style scope>

</style>