<template>
    <div class="dialog_form">
        <el-form ref="formData" :model="formData" :rules="rules" size="small" label-width="100px">

            <el-form-item label-width="85px" label="标题:" prop="infoTitle">
                <el-text>{{ formData.infoTitle }}</el-text>
            </el-form-item>
            <el-row>
                <el-col :span="2">
                    <el-form-item label-width="85px" label="附件:" required="true"></el-form-item></el-col>
                <el-col :span="8">
                    <vsfileupload class="uploadFile" v-if="showUpload" ref="upload" :busId="fileParams.busId"
                        :ywlb="formData.infoType + 'file'" :editable="false">
                    </vsfileupload>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="8">
                    <el-form-item label-width="85px" label="上报单位:" prop="operatororgName">
                        <el-text>{{ formData.operatororgName }}</el-text>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label-width="80px" label="上报人:" prop="operatorName">
                        <el-text>{{ formData.operatorName }}</el-text>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label-width="80px" label="上报时间:" prop="publishTime">
                        <el-text>{{ formData.publishTime }}
                        </el-text>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label-width="85px" label="是否同意:" prop="status">
                <el-radio v-model="formData.status" value="1" label="1">同意</el-radio>
                <el-radio v-model="formData.status" value="0" label="0">不同意</el-radio>
            </el-form-item>
            <el-form-item label-width="85px" label="审核意见:" prop="oponion">
                <el-input v-model="formData.oponion" type="textarea" placeholder="请输入审核意见"
                    :autosize="{ minRows: 4, maxRows: 16 }" :style="{ width: '80%' }" :maxlength="4000" show-word-limit>
                </el-input>
            </el-form-item>
            <el-form-item size="large" align="center" class="button_style">
                <el-button type="primary" @click="saveData()">保存</el-button>
                <el-button @click="closeForm">返回</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import vsfileupload from '../../../common/vsfileupload.vue';
import { v4 as getUUID } from 'uuid';
import VSAuth from "@vsui/lib-vueauth4vseaf";
export default {
    components: {
        vsfileupload,
    },
    props: {
        rowData: Object,//列表传入的行数据
        type: {
            type: String,
            default: ""
        },
    },
    data() {
        return {
            fileParams: {
                busId: '',//业务ID
                ywlb: '',//附件业务类型，区分同一数据多个文件类型
            },
            formData: {
                status: '',//审核意见
            },

            rules: {
                infoTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],
                status: [{ required: true, message: '请选择审核意见', trigger: 'change' }],
                oponion: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],

            },
            showUpload: false
        }
    },
    mounted() {
        this.formData.infoId = this.rowData.infoId;
        this.getInfoData();
    },
    methods: {
        //保存信息
        saveData() {
            var isValid = false;
            this.$refs.formData.validate((valid) => {
                isValid = valid;
            });
            if (!isValid) {
                this.$message({ message: '页面有必填项未填写', type: 'error' });
                return;
            }
            if (this, this.formData.status == '1') {
                if (this.type == 'dzbsjsh') {
                    this.formData.processStatus = '2';
                } else {
                    this.formData.processStatus = '3';
                }
            } else {
                this.formData.processStatus = '4';
            }
            this.formData.monitoeId = getUUID().replace(/-/g, '').toUpperCase();
            this.formData.userId = VSAuth.getAuthInfo().permission.userId;
            this.formData.userName = VSAuth.getAuthInfo().permission.userName;
            this.formData.orgId = VSAuth.getAuthInfo().permission.orgnaId;
            this.formData.orgName = VSAuth.getAuthInfo().permission.orgnaName;
            this.axios({
                method: "post",
                url: "/backend/qlwzsh/saveInfoDataMonitor",
                data: this.formData

            }).then(response => {
                if (response.data.data.status == 'success') {
                    this.$message.success("保存成功！");
                    this.closeForm();
                } else {
                    this.$message.error("保存失败！");
                }
            }).catch(error => {
                this.$message.error("保存失败！")
            })


        },
        //获取某条数据:根据infoId查询数据
        getInfoData() {
            this.axios({
                method: "post",
                url: "/backend/qlwz/queryInfoDataById",
                data: {
                    infoId: this.formData.infoId,
                },
            }).then(resp => {
                if (resp.data.data && resp.data.data.infoId) {
                    this.formData = resp.data.data;
                    this.fileParams.busId = this.formData.infoId;
                }
                this.showUpload = true;
            })

        },
        //关闭弹窗
        closeForm() {
            this.$emit('handleCloseViewDialog');
        },
    }


}
</script>
<style scoped>
.uploadFile {
    margin-top: -24px;
    margin-left: 25px;
}

.button_style {
    margin-left: -100px;
}
</style>