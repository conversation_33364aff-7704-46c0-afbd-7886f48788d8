'use strict'
const utils = require('./utils')
const webpack = require('webpack')
const buildConfig = require('../config/index.js')
const merge = require('webpack-merge')
const chalk = require('chalk')
const baseWebpackConfig = require('./webpack.base.conf')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const FriendlyErrorsPlugin = require('friendly-errors-webpack-plugin')
const portfinder = require('portfinder')


let HOST = process.env.HOST || buildConfig.dev.host ;
let PORT = (process.env.PORT && Number(process.env.PORT))||buildConfig.dev.port;

const devWebpackConfig = merge(baseWebpackConfig, {
  mode:"development",
  entry: {
    app: ['./examples/main.js']
  },
  module: {
    rules: utils.styleLoaders({ sourceMap: buildConfig.dev.cssSourceMap, usePostCSS: true })
  },
  // cheap-module-eval-source-map is faster for development
  devtool: buildConfig.dev.devtool,

  // these devServer options should be customized in /config/index.js
  devServer: 
  {
    clientLogLevel: 'warning',
    historyApiFallback: true,
    hot: true,
    compress: true,
    host: HOST,
    port: PORT,
    open: buildConfig.dev.autoOpenBrowser,
    overlay: buildConfig.dev.errorOverlay
      ? { warnings: false, errors: true }
      : false,
    publicPath: '/',
    proxy: buildConfig.dev.proxyTable,
    quiet: true, // necessary for FriendlyErrorsPlugin
    watchOptions: {
      poll: buildConfig.dev.poll,
    }
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env': require('../config/dev.env')
    }),
    new webpack.HotModuleReplacementPlugin(),
    new webpack.NamedModulesPlugin(), // HMR shows correct file names in console on update.
    new webpack.NoEmitOnErrorsPlugin(),
    // https://github.com/ampedandwired/html-webpack-plugin
    new HtmlWebpackPlugin({
      
      filename: 'index.html',
      template: 'index.html',
      inject: false,
      templateParameters: (compilation, assets, assetTags, options) => {
        return {
                  compilation,
                  webpackConfig: compilation.options,
                  htmlWebpackPlugin: {
                      tags: assetTags,
                      files: assets,
                      options,
                  },
                  prod:false,
              }
        },
    }),
  ]
})
const SUCCMSG=chalk.magentaBright(utils.sign+`

    [框架示例模式，如想运行您基于框架开发的项目请运行：npm run dev]

    最后编译时间：{date}

    应用程序运行在这里: ${chalk.white("http://{host}:{port}{path}")}`)

module.exports = new Promise((resolve, reject) => {
  portfinder.basePort = buildConfig.dev.port
  portfinder.getPort((err, port) => {
    if (err) {
      reject(err)
    } else {
      // publish the new Port, necessary for e2e tests
      process.env.PORT = port
      // add port to devServer config
      devWebpackConfig.devServer.port = port
      PORT=port;
      // Add FriendlyErrorsPlugin
      devWebpackConfig.plugins.push(new FriendlyErrorsPlugin({
        compilationSuccessInfo: {
          
          messages: [SUCCMSG.format({
            "date":(new Date()).format("yyyy-MM-dd hh:mm:ss"),
            "host":HOST,
            "port":PORT,
            "path":devWebpackConfig.devServer.publicPath
          })]
        },
        onErrors: buildConfig.dev.notifyOnErrors
        ? utils.createNotifierCallback()
        : undefined
      }))

      resolve(devWebpackConfig)
    }
  })
})
