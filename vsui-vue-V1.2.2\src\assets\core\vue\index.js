import Vue from "vue";



//取消 Vue 所有的日志与警告。
Vue.config.silent=true;

//配置是否允许 vue-devtools 检查代码
Vue.config.devtools = process.env.NODE_ENV === 'production'?false:true;

/**
 * 指定组件的渲染和观察期间未捕获错误的处理函数。这个处理函数被调用时，可获取错误信息和 Vue 实例。
 * @param {*} err handle error
 * @param {*} vm  
 * @param {*} info `info` 是 Vue 特定的错误信息，比如错误所在的生命周期钩子
 */
//Vue.config.errorHandler=(err, vm, info)=>{};

//Vue.config.warnHandler=(msg, vm, trace)=>{};

Vue.config.ignoredElements=
[
    // 用一个 `RegExp` 忽略所有“ion-”开头的元素
    // 仅在 2.5+ 支持
    /^ion-/
];

//给 v-on 自定义键位别名,如：<input type="text" @keyup.media-play-pause="method">
Vue.config.keyCodes=
{
  // camelCase 不可用
  mediaPlayPause: 179,
  // 取而代之的是 kebab-case 且用双引号括起来
  "media-play-pause": 179,
  up: [38, 87]
};

//设置为 true 以在浏览器开发工具的性能/时间线面板中启用对组件初始化、编译、渲染和打补丁的性能追踪。只适用于开发模式和支持 performance.mark API 的浏览器上。
Vue.config.performance=false;

//设置为 false 以阻止 vue 在启动时生成生产提示。
Vue.config.productionTip=process.env.NODE_ENV === 'production'?false:true;

export default Vue;