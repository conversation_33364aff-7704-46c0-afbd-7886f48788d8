<template>

    <div class="left-navs">
      <div class="left_navs_fold">
        <!--快链区默认不显示，如需使用，请取消注释-->
        <!--<div class="shortcuts hide" :class="isCollapse?'hide':'show'">
          <div class="lf shortcut">快链1</div>
          <div class="lf shortcut">快链2</div>
          <div class="lf shortcut">快链3</div>
          <div class="lf shortcut">快链4</div>
          <div class="lf shortcut plus"><i class="fa fa-plus"/></div>
        </div>-->
        <div class="shortcuts" :class="isCollapse?'hide':'show'">
          <el-input class="searchinput" v-model="searchKeys" placeholder="请输入检索内容">
            
          </el-input>
        </div>
        <div class="collapse" @click="isCollapse=!isCollapse">
          <i class="fa " :class="isCollapse? 'fa-arrow-right' : 'fa-arrow-left'"></i>
        </div>
      </div>

      <el-menu class="el-menu-scoped" :default-openeds="openedModule" @open="handleOpen" @close="handleClose" @select='handleSelect' :collapse="isCollapse">
        <template v-for="(module) in _modules">
          <el-submenu v-if="module.children && module.children.length > 0" 
            :key="module.resId" :index="module.resId" class="el-menu-item-scoped">
            <template slot="title" class="el-submenu__title" taget="li">
              <i v-if="module.iconClass" :class="module.iconClass" :style="module.iconColor!=''?{'color':module.iconColor}:''" ></i>
              <span class="el-submenu-item-title-scoped" v-html="module.resName"></span>
            </template>
            <el-tree :data="module.children" default-expand-all :node-key="module.resId" :props="{label:'resName',children: 'children'}" accordion highlight-current="false" expand-on-click-node="false" check-on-click-node="true" @node-click="tree_node_click" class="el-tree-scoped">
              <span class="el-tree-node-scoped" slot-scope="{ node, data }">
                <i v-if="data.iconClass" :class="data.iconClass" :style="data.iconColor!=''?{'color':data.iconColor}:''" ></i>
                <span  class="custom-tree-node" v-html="data.resName" >{{data.resName}}</span>
              </span>
            </el-tree>
          </el-submenu>
          <el-menu-item v-else 
          :key="module.resId" :index="module.resId" class="el-menu-item-scoped" >
            <i v-if="module.iconClass" :class="module.iconClass" :style="module.iconColor!=''?{'color':module.iconColor}:''" ></i>
            <span class="el-submenu-item-title-scoped" v-html="module.resName"></span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
</template>


<script scoped>
import {getResPvalueByResId,searchModulesTree} from "../../lib/comFun"
export default {
  data() {
    return {
      isCollapse: true,
      index:1,
      searchKeys:"",
    };
  },
  props: { 
    modules: { 
      default: [] 
    }, 
    clickMode:{
      
    }
  },
  methods: {
    handleOpen(resId, indexPath) {
      
      console.log("打开"+resId, indexPath);
    },
    handleClose(resId, indexPath) {
      
      console.log("关闭"+resId, indexPath);
    },

    handleSelect(resId, indexPath) {
      let res=getResPvalueByResId(resId)
      if(res&&res.resPvalue!="") 
      {
        this.$emit("beforemoduleclick",res.resPvalue); 
        this.$router.push({path:res.resPvalue});
        this.$emit("moduleclicked",res.resPvalue);
        console.log("点击"+res.resPvalue, indexPath);   
      }
      
    },
    tree_node_click(module,node,node1) {
      if(module.resId&&module.resId!=""&&module.resPvalue!="") 
      {
        //此处调用父子组件事件触发，区别于LeftTopNavs.vue中全局事件总线
        this.$emit("beforemoduleclick",module.resPvalue); 
        this.$router.push({path:module.resPvalue});
        this.$emit("moduleclicked",module.resPvalue);
        console.log("点击"+module.resPvalue)
      }
    },

  },
  computed:{
    _modules(){
      if(this.searchKeys!=""&&this.modules&&this.modules.length>0){
        return searchModulesTree(this.modules,"resName",this.searchKeys);
      }
      else{
        return this.modules;
      }
    },
    //当前需要展开的菜单,返回控件key绑定的属性值的数组
    openedModule(){
      let opened=[];
      this._modules.forEach(module => {
        opened.push(module.resId);
      });
      if(this.searchKeys!="")
      {
        return opened;
      }
      else
      {
        return [];
      }
        
    }    
  },
    
  mounted() {
    
  },
};
</script>