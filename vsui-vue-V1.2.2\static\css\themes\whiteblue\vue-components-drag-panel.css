
.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout{

}
.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-gridding{
    border:1px solid #666666;
    background: #cccccc;
    box-sizing: border-box;
    float: left;
    display: block;
    position: relative;
}

.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item
{
    border: 1px solid #cccccc;
    background: #F2F2F2;
    border-radius: 0px;
    box-sizing: border-box;
}

.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item.vue-grid-placeholder
{
    background: #2384B7;
    opacity: 0.5;
}

.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item i.vue-grid-item-delbtn
{
    position: absolute;
    cursor: pointer;
    top: 5px;
    right: 5px;
}

.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item .vue-grid-item-tools
{
    position: absolute;
    cursor: pointer;
    top: 0px;
    right: 0px;
}
.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item .vue-grid-item-tools .vue-grid-item-tool
{
    width:40px;
    border-left: 1px  solid #cccccc;
    text-align: center;
}
.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item .vue-grid-item-tools em
{
    width:20px;
}

.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item .vue-grid-item-tools .vue-grid-item-tool .loading
{
    -webkit-transition-property: -webkit-transform;
    -webkit-transition-duration: 1s;
    -moz-transition-property: -moz-transform;
    -moz-transition-duration: 1s;
    -webkit-animation: rotate 3s linear infinite;
    -moz-animation: rotate 3s linear infinite;
    -o-animation: rotate 3s linear infinite;
    animation: rotate 3s linear infinite;
}

@-webkit-keyframes rotate{from{-webkit-transform: rotate(0deg)}
    to{-webkit-transform: rotate(360deg)}
}
@-moz-keyframes rotate{from{-moz-transform: rotate(0deg)}
    to{-moz-transform: rotate(359deg)}
}
@-o-keyframes rotate{from{-o-transform: rotate(0deg)}
    to{-o-transform: rotate(359deg)}
}
@keyframes rotate{from{transform: rotate(0deg)}
    to{transform: rotate(359deg)}
}

.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item .vue-grid-item-titleline
{
    line-height: 40px;
    height:40px;
    box-sizing: border-box;
    border-bottom: 1px solid #cccccc;
}
.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item .vue-grid-item-titleline .vue-grid-item-title
{
    font-weight: 500;
    padding-left: 10px;
}
.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-item .vue-grid-item-contentline
{
    position: relative;
    height: -moz-calc(100% - 40px);
    height: -webkit-calc(100% - 40px);
    height: calc(100% - 40px);
    margin: 0px 0px;
    box-sizing: border-box;
    border:0px solid #ccc;
}
.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main .vue-grid-layout .vue-grid-placeholder{

}