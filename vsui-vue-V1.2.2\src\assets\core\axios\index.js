/*
by cuiliang
20180914
用途：对axios库的请求，应答进行拦截，对请求与应答过程中出现的错误进行统一处理


*/


import Axios from 'axios'

import {axiosInterceptor0,axiosInterceptor2} from './axios.interceptor.js'

Axios.defaults.timeout=20000;



/**
 * 系统层拦截器拦截器，处于拦截器最外层，会将其他的请求与应答拦截器包括在内部，鉴权模块抛出的错误将不会在此层拦截到
 * 其特点是：会最后收到请求拦截，最早收到收到应答拦截
 * 
 * 如下图例展示了多个axios拦截器的工作机制
 * 
 *     
 *   
 * 
 *                ↗-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-↘
 *                ↑                                                                                                 |
 *     -----------|----------                                                                                       ↓
 *     -----------↑----------       先加入                                                                           |
 *     -----------|----------  axiosInterceptor0  request                                                           ↓
 *     -----------↑----------  axiosInterceptor1  request  这里是@vsui/lib-vueauth4vseaf组件的axios拦截器             |
 *     -----------|----------  axiosInterceptor2  request                                                           ↓
 *     -----------↑----------       后加入                                                                           |
 *     -----------|----------                                                                                       ↓
 *                ↑                                                                                                 |
 *             AXIOS请求                                                                                            ↓
 *                ↑                                                                                                 |
 *     -----------|----------                                                                                       ↓
 *     -----------↑----------       后加入                                                                           |
 *     -----------|----------  axiosInterceptor2  response                                                          ↓   
 *     -----------↑----------  axiosInterceptor1  response  这里是@vsui/lib-vueauth4vseaf组件的axios拦截器            |                                                                          |
 *     -----------|----------  axiosInterceptor0  response                                                          ↓
 *     -----------↑----------       先加入                                                                           |
 *     -----------|----------                                                                                       ↓
 *                ↑                                                                                                 |
 *                ↖-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-↙
 * 
 * 
 */


//系统级axios拦截器在这里被加入，处于第一层；
let requestIndex0=Axios.interceptors.request.use(axiosInterceptor0.request,axiosInterceptor0.requestError);
let responseIndex0=Axios.interceptors.response.use(axiosInterceptor0.response,axiosInterceptor0.responseError);
console.log(`axios拦截器注册完毕，请求拦截器序号为：${requestIndex0}，应答拦截器序号为：${responseIndex0}`);


//如果使用@vsui/lib-vueauth4vseaf组件，鉴权拦截器将在此步被自动加入，处于第二层；

//应用及axios拦截器在这里被加入，处于第三层
window.setTimeout(function(){
    let requestIndex2=Axios.interceptors.request.use(axiosInterceptor2.request,axiosInterceptor2.requestError);
    let responseIndex2=Axios.interceptors.response.use(axiosInterceptor2.response,axiosInterceptor2.responseError);
    console.log(`axios拦截器注册完毕，请求拦截器序号为：${requestIndex2}，应答拦截器序号为：${responseIndex2}`);
},1);

export default Axios
