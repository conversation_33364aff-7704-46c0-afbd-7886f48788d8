<template>
    <div>
        <div>
            <iframe src="/static/header.html" ref="iframe" width="100%" scrolling="no" height="210px"
                style="border: none;"></iframe>
        </div>
        <div class="zhyy-list-container" style="width: 1200px;margin: 0 auto;">
            <div class="zhyy-list-main" style="padding: unset">
                <div class="zhyy-list-searchArea" style="box-shadow: unset">
                    <el-row>
                        <el-col :span="24">
                            <label>标题：</label>
                            <el-input
                                v-model="listQuery.title"
                                placeholder="标题"
                                style="width: 200px"
                                clearable
                            ></el-input>
                            <el-button type="primary" icon="el-icon-search" style="margin-left: 10px" @click="processSearch()" >查询</el-button>
                        </el-col>
                    </el-row>
                </div>
                <el-row style="border-radius: 5px;background-color: #fff;" >
                    <el-table 
                        stripe
                        border
                        v-loading="listLoading" 
                        highlight-current-row
                        :show-header="false"
                        ref="listTable"
                        :data="tableData"
                        height="calc(100vh - 415px)"
                        >
                        <el-table-column align="center" label="序号" type="index" :index="indexMethod" width="60px" >
                        </el-table-column>
                        <el-table-column min-width="300px" header-align="left" >
                            <template slot-scope="scope">
                                <span @click="clickTitle(scope.row.INFOID)">
                                    {{scope.row.INFO_TITLE}}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column min-width="100px" align="center" prop="PUBLISH_TIME">
                        </el-table-column>
                    </el-table>
                    <div class="zhyy-list-paginationArea">
                        <el-pagination
                            background
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="listQuery.page"
                            :page-sizes="[10, 20, 30, 40]"
                            :page-size="listQuery.size"
                            layout="total, sizes, prev, pager, next, jumper "
                            :total="total"
                        ></el-pagination>
                    </div>
                </el-row>
            </div>
        </div>
        <div style="width: 1200px; margin: auto;">
            <iframe src="/static/footer.html" ref="iframe" width="100%" scrolling="no" style="border: none;"></iframe>
        </div>
    </div>

</template>
<script>
export default {
    props: {
        type: {
            type: String,
            default: ""
        },
    },
    data() {
        return {
            listLoading: false,
            listQuery: {
                page: 1,
                size: 10,
                type: '',
                title: '',
            },
            tableData: [],
            total: 0,
        }
    },
    mounted() {
        this.initData();
    },
    methods: {
        // 初始化数据
        initData() {
            this.listLoading = true;
            this.listQuery.type = this.type;
            this.axios({
                method: 'get',
                url: '/backend/qlwz/queryInfoWebList',
                params: this.listQuery,
            }).then(response => {
                this.listLoading = false;
                this.tableData = response.data.data.rows;
                this.total = response.data.data.total;

            }).catch(() => {
                this.$message('查询失败！')
            })
        },
        // 点击标题
        clickTitle(infoid){
            window.open('/infoWebView?id='+infoid);
        },
        processSearch() {
            this.listQuery.page = 1;
            this.initData();
        },
        handleSizeChange(val) {
            this.listQuery.size = val;
            this.initData();
        },
        handleCurrentChange(val) {
            this.listQuery.page = val;
            this.initData();
        },
        indexMethod(index) {
            return index + this.listQuery.size * (this.listQuery.page - 1) + 1;
        },

    }
}
</script>
<style scoped>
</style>