<template>
    <div>
        <div>
            <iframe src="/static/header.html" ref="iframe" width="100%" scrolling="no" height="210px"
                style="border: none;"></iframe>
        </div>
        <div style="width: 1200px;height: calc(100vh - 300px);margin: 0 auto;">
            <div style="line-height: 74px;color: #0f3a97;font-size: 18px;text-align: center">
                {{form.INFO_TITLE}}
            </div>
            <div style="width: 80%;margin: 0 auto;border-bottom: 1px dashed #ccc;line-height: 2em;height: 25px;text-align: right;">
                <span>单位 : {{form.OPERATORORGNAME}} - {{form.OPERATORNAME}}</span>
                <span style="margin-left: 15px;">日期 : {{form.PUBLISH_TIME}}</span>
            </div>
            <el-row style="width: 80%;margin: 0 auto;">
                <el-col :span="2" style="margin-top: 32px;">
                    附件 : 
                </el-col>
                <el-col :span="22">
                    <vsfileupload v-if="form.INFOID" :editable="false" :busId="form.INFOID" :ywlb="form.INFO_TYPE+'file'">
                    </vsfileupload>
                </el-col>
            </el-row>
        </div>
        <div style="width: 1200px; margin: auto;">
            <iframe src="/static/footer.html" ref="iframe" width="100%" scrolling="no" style="border: none;"></iframe>
        </div>
    </div>

</template>
<script>
import vsfileupload from '../../../common/vsfileupload.vue';
export default {
    components: {
        vsfileupload,
    },
    data() {
        return {
            id: this.$route.query.id,
            form:{
                INFOID: '',
                INFO_TYPE: '',
                INFO_TITLE: '',
                INFO_NOTES: '',
                PUBLISH_TIME: '',
                OPERATORID: '',
                OPERATORNAME: '',
                OPERATORORGID: '',
                OPERATORORGNAME: ''
            }
        }
    },
    mounted() {
        this.initData();
    },
    methods: {
        // 初始化数据
        initData() {
            this.axios({
                method: 'get',
                url: '/backend/qlwz/queryInfoWebOne',
                params: {id: this.id},
            }).then(response => {
                this.form = response.data.data.form;

            }).catch(() => {
                this.$message('查询失败！')
            })
        },

    }
}
</script>
<style scoped>
</style>