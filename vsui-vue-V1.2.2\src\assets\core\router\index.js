import Vue from "vue";
import Router from "vue-router";
import routerDefine from "./router.define.js";
import {routerInterceptor0,routerInterceptor2} from "./router.interceptor";
import runtimeCfg  from "../runtime/config.js";

//if(process.env.NODE_ENV === 'production'){
  const originalPush = Router.prototype.push;
  Router.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err);
  }
  const originalReplace = Router.prototype.replace;
  Router.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch(err => err);
};
//}


Vue.use(Router);

let router = new Router({
  mode: "history",
  base: process.env.NODE_ENV === 'production'
  ? runtimeCfg.app_public_path : "/" ,
  routes:routerDefine,
});


/* 如下图例展示了多个路由拦截器的工作机制
* 
*     
*                ↗-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-→-↘
*                ↑                  后加入                                                                         |
*     -----------|----------  routerInterceptor2  beforeEach                                                       ↓
*     -----------↑----------  routerInterceptor1  beforeEach  这里是@vsui/lib-vueauth4vseaf组件的路由拦截器          |
*     -----------|----------  routerInterceptor0  beforeEach                                                       ↓
                                    先加入
*                ↑                                                                                                 |
*             路由跳转                                                                                              ↓
*                ↑                   后加入                                                                         |
*     -----------|----------  routerInterceptor2  afterEach                                                        ↓
*     -----------↑----------  routerInterceptor1  afterEach   这里是@vsui/lib-vueauth4vseaf组件的路由拦截器          |
*     -----------|----------  routerInterceptor0  afterEach                                                        ↓
*                ↑                   先加入                                                                         |
*                ↖-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-←-↙
* 
* 
*/


//系统级router拦截器在这里被加入，处于第一层；
router.beforeEach(routerInterceptor0.beforeEach);
let beforeIndex0=router.beforeHooks.length-1
router.afterEach(routerInterceptor0.afterEach);
let afterIndex0=router.afterHooks.length-1

console.log(`路由拦截器注册完毕，请求拦截器序号为：${beforeIndex0}，应答拦截器序号为：${afterIndex0}`);

//如果使用@vsui/lib-vueauth4vseaf组件，鉴权拦截器将在此步被自动加入，处于第二层；

//应用级router拦截器在这里被加入，处于第三层
window.setTimeout(function(){
  router.beforeEach(routerInterceptor2.beforeEach);
  let beforeIndex2=router.beforeHooks.length-1
  router.afterEach(routerInterceptor2.afterEach);
  let afterIndex2=router.afterHooks.length-1
    console.log(`路由拦截器注册完毕，请求拦截器序号为：${beforeIndex2}，应答拦截器序号为：${afterIndex2}`);
},1);





export default router;
