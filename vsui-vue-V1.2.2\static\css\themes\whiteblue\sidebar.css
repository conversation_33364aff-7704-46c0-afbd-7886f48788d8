/**********************左侧菜单*****************************/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped{
  border-right: 1px solid #cccccc;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold {
  
  /* background: #F2F2F2; */
  background:#ffffff;
  border-bottom: 1px solid #cccccc;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold:hover{
  
}

/**和并展开小箭头**/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse{
  color:#333333;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse:hover{
  color:#111111;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse>i {
  
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse>i:hover {
  
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts{

}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div {
  color:#333333
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div:hover {
  color: #111111;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div.shortcut {
  border: 1px solid #cccccc;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div.plus {

  border: 1px dotted #cccccc;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped {
  /* background-color: #F2F2F2; */
  background-color: #ffffff;
  border-right: 0px solid #cccccc;
  color: #333333;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-submenu__title {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped.el-menu:not(.el-menu--collapse) {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped.el-menu--collapse {
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped{
  /* background-color: #F2F2F2; */
  background-color: #ffffff;
  color: #333333; 
  }
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped:hover
{
  background-color: #cccccc;
  color: #111111; 
}
/*********左侧菜单图标字体设置*************/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped i{
  color:#333333;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped:hover i{
  color: #111111;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active] .el-submenu-item-title-scoped{
  color: #000000;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title{
  color: #333333;
  background: transparent;
}
  .app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title i{
  color: #333333;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover{
  color: #111111;
  background: transparent;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover i {
  color: #111111;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu-item-title-scoped{
  color: #333333; 
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu-item-title-scoped .red{
  color: red ;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped {
  background: #ffffff;
  /* background: #F2F2F2; */
}


.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content
{
  color:#333333;
  background: transparent;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content:hover{
  color: #111111 !important;
  background-color: #ddd !important;
}


.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node[class*="is-current"] > .el-tree-node__content {
  color: #000000 ;
  background-color: #ddd;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped i{
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped .custom-tree-node {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped .custom-tree-node .red {
  color:red;
}
/* 左侧导航样式 结束 */