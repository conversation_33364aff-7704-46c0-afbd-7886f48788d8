<template>
    <div class="top">
        <div class="wrap-con">
            <div v-for="(item,index) in fileList" :key="index" style="display: inline-block">
                <div class="wrap-x">
                    <div class="center1">
                        <div class="rect">
                            <div class="rect-con"></div>
                            <div class="xxx" @click="show(item.url)">
                                <el-image style="width: 192px; height: 108px;" :src="item.url"></el-image>
                            </div>
                        </div>
                        <div style="text-align: center;height: 50px;line-height: 50px">
                            <span>{{item.name}}</span>
                            <!-- <el-input class="input" placeholder="请输入内容" v-model="item.name" clearable></el-input> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <el-dialog  v-dialogDrag 
            v-if="showOnline"
            title="在线打开"
            fullscreen
            :visible.sync="showOnline" 
            :before-close="handleCloseOnlineDialog" 
            :close-on-click-modal="false" >
            <showOnline :url="url" @closeOnlineDialog="handleCloseOnlineDialog"/>

        </el-dialog>
    </div>
</template>

<script>
import axios from "@/axios";
import showOnline from './online.vue'
export default {

    components: {
        showOnline
    },
    data() {
        return {
            input: '',
            fileList: [{"url":"/static/img/dpzs/dpzs.png","name":"胜利油田市场运行管理系统大屏"},
                       {"url":"/static/img/pic.png","name":"胜利油田大监督信息平台"},
                       {"url":"/static/img/symb/rlzy.png","name":"胜利油田员工培训系统"},
                       {"url":"/static/img/symb/scpt.png","name":"中原油田市场管理系统"}
                       ],
            showOnline:false
        }
    },
    methods: {
        show(url){
            this.url = url;
            this.showOnline = true;
        },
        handleCloseOnlineDialog(){
            this.showOnline = false;
        },

    },
    mounted() {

    },
}
</script>

<style>

.center1 {
    width: 280px;
    height: 260px;
}

.top {
    width: 85vw;
    height: auto;
}
.wrap-con {
    width: 100%;
    justify-content: left;
    align-items: center;
    flex-wrap: wrap;
}
.wrap-x {
    width: 280px;
    height: 220px;
    margin: 20px 10px;
}
.rect {
    width: 240px;
    height: 190px;
    margin-left: 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}
.rect-con {
    background-color: #e2ecf6;
    width: 240px;
    height: 190px;
    border-radius: 4px;
    box-shadow: inset 0 -20px 30px rgba(47, 74, 99, 0.2);
    display: flex;
    justify-content: left;
    align-items: center;
}

.xxx {
    width: 192px;
    height: 108px;
    background-size: 100%, 100%;
    position: absolute;
    transition: all 0.5s;
}
.xxx:hover {
    transform: scale(1.5);
}

image {
    margin-left: 20px;
}

.el-dialog {
    max-height: calc(100vh);
}
.el-dialog__body {
    max-height: calc(100vh);
}

</style>