@import url("home.css");

/********************

本皮肤由崔良创建于20200413，

白蓝色：
白为主色调
蓝为辅色调
字为灰阶黑

白色文字：#ffffff
白色半透明背景色：#f4f6f4




************************/





/********************************************以下为皮肤配色********************************************/
html,
body,
.app,
.wrapper {
  background-color: #2384B7;
  transition: all 0.3s ease-in-out;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
   /*  background-color: #f6f6f6; */

    /*width:4px;
    height:4px;*/
  }
  
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 3px #fff;
    background: #fff;

  }
::-webkit-scrollbar-track-piece
  {}
  /*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 3px #cfcfd0;
    background-color: #cfcfd0;

    
  }
  ::-webkit-scrollbar-thumb:hover {
    -webkit-box-shadow: inset 0 0 3px #c1c1c1;
    background-color: #c1c1c1;

  }
  ::-webkit-scrollbar-corner{
    background: F6F6F6;
  }
  
  ::-webkit-scrollbar-button:horizontal:decrement {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #999;

  }
  ::-webkit-scrollbar-button:vertical:decrement {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #999;
  }
  ::-webkit-scrollbar-button:horizontal:decrement:hover {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #E7E7E7;
  }
  ::-webkit-scrollbar-button:vertical:decrement:hover {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #E7E7E7;
  }
  .app {
    transition: all 0.3s ease-in-out;
    background-color:transparent;
    opacity:1;
  }
  .app.changingtheme {
    transition: all 0.3s ease-in-out;
    opacity:0;
  }

 