{
  "presets": [
    ["@babel/preset-env",
      {
        "useBuiltIns":"entry",
        "modules":false,
        "corejs": "2.6",
        "targets": {
            "browsers": ["> 1%", "last 5 versions", "not ie <= 8"],
            "chrome": "58",
            "ie": "9"
        },
        "debug":true
      }
    ],
  ],
  "plugins": [
    /*"transform-vue-jsx",*/
    ["@babel/transform-runtime",
      {
        "absoluteRuntime": false,
        "helpers": true,
        "regenerator": true,
        "useESModules": false,
        "proposals": true
      }
    ],
    "@babel/plugin-transform-arrow-functions",
    /*"@babel/plugin-proposal-class-properties"
    // 笔者为了兼容IE8才用了这个插件，代价是不能tree shaking
    // 没有IE8兼容需求的同学可以把这个插件去掉
    "@babel/plugin-transform-modules-commonjs"*/

  ],
  "env": {
    "test": {
      "presets": ["@babel/preset-env"]
    }
  }
}



