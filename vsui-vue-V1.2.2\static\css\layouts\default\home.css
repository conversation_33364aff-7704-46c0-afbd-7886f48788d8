@import url("header.css");
@import url("footer.css");
@import url("sidebar.css");
@import url("container.css");


/*
* .el-main-scoped .el-tabs-scoped对应工作区是选项卡模式，即多页面模式
* .el-main-scoped .el-card-scoped对应工作区是单面板模式，即单页面模式
*
*
*
*
*
*
*
*
*
*
*
*/














/**************************主内容区域***********************************/
.app .wrapper .content-box {
  position: relative;
  height: -moz-calc(100%); /*减顶部100px和底部30px*/
    height: -webkit-calc(100%);
    height: calc(100%);
  
  overflow-y: hidden;
  overflow-x: hidden;
}
/************************头部收起时主内容区域*************************/
.app .wrapper .header-box.hidestate+div.content-box {
  /*头部隐藏状态时的主区域高度*/
  height: -moz-calc(100%); /*减底部30px*/
  height: -webkit-calc(100%);
  height: calc(100%);
}
.app .wrapper .content-box.content-box-hiddenHeader {
  position: relative;
  height: -moz-calc(100%);
    height: -webkit-calc(100%);
    height: calc(100%);
  
  overflow-y: hidden;
  overflow-x: hidden;
}

.app .wrapper .content-box .content {
  width: auto;
  padding: 0;
  height: 100% !important;
}

.app .wrapper .content-box .content .el-container-scoped {
  position: relative;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 左侧导航样式 */
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped {
    box-sizing: border-box;
  -webkit-box-sizing: border-box;
  position: relative;
  
}

/* 内容区 背景色 */
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped-load {
  position: relative;
  left: 400px;
  padding: 0px;
}

.app .wrapper .content-box .content .el-container-scoped .el-main-scoped {
  position: relative;
  padding: 0px;

}



/**

begin 单面板工作区下的样式定义


以下两条分别对应：
适配上、下区域模式下（如dashboard页面）下方工作区布局
适配上、下左右区域模式下（如vsui-vue/v1-1-0-index页面）下右方为单面板工作区布局

*/
.app .wrapper .content-box .content .el-card-scoped,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped
 {
  box-sizing: border-box;
  border: 0px;
  border-radius: 0px;
  /* border-top: 2px solid #5fa1c6; */
  margin: 0 0;
  height:100%;
}

/**
以下两条分别对应单面板模式下的适配：
适配上、下区域模式下（如dashboard页面）下方为单面板时，面板头部布局样式
适配上、下左右区域模式下（如vsui-vue/v1-1-0-index页面）下右方为单面板时，面板头部布局样式
*/
.app .wrapper .content-box .content .el-card-scoped > .el-card__header,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__header
{
  padding: 4px 20px ;
  line-height: 32px ;
  height: 40px ;
  -webkit-box-sizing: border-box ;
  box-sizing: border-box ;
}


/*默认头部标题字体样式 */
.app .wrapper .content-box .content .el-card-scoped > .el-card__header .title_bar,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__header .title_bar
{
  font-size: 16px;
  font-weight: bold;
}



.app .wrapper .content-box .content .el-card-scoped > .el-card__header .el-button-scoped,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__header .el-button-scoped{
  width: 32px;
  height: 32px;
  padding: 0px;
  margin: 0 2px;
}

/**end 单面板情况下的样式定义*/


/**

begin 选项卡情况下的样式定义
适配上、下左右区域模式下（如aboutme页面）下右方为选项卡工作区布局
以下两条分别对应：
适配上、下区域模式下（如dashboard页面）下方工作区布局
适配上、下左右区域模式下（如vsui-vue/v1-1-0-index页面）下右方为单面板工作区布局

*/

.app .wrapper .content-box .content .el-tabs-scoped,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped{
  box-sizing: border-box;
  border: 0px;
  border-radius: 0px;
  margin: 0 0 ;
  height:100%;
}

/**
以下两条分别对应选项卡模式下的适配：
适配上、下区域模式下（如dashboard页面）下方为选项卡时，面板头部布局样式
适配上、下左右区域模式下（如aboutme页面）下右方为选项卡时，面板头部布局样式
*/
.app .wrapper .content-box .content .el-tabs-scoped > .el-tabs__header,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped > .el-tabs__header
{
    height: 40px ;
    -webkit-box-sizing: border-box ;
    box-sizing: border-box ;
    margin: 0 0 ;
}

.app .wrapper .content-box .content .el-tabs-scoped > .el-tabs__header .el-tabs__nav ,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped > .el-tabs__header .el-tabs__nav 
{
  border-radius: 0px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 0 0 0 0;
}

.app .wrapper .content-box .content .el-tabs-scoped > .el-tabs__header .el-tabs__nav .el-tabs__item,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped > .el-tabs__header .el-tabs__nav .el-tabs__item
{
  font-size: 14px;
  font-weight: normal;
}

.app .wrapper .content-box .content .el-tabs-scoped > .el-tabs__header .el-tabs__nav .el-tabs__item[class~=is-active],
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped > .el-tabs__header .el-tabs__nav .el-tabs__item[class~=is-active]
{
  font-size: 14px;
  font-weight: bolder;
}

.app .wrapper .content-box .content .el-tabs-scoped > .el-tabs__header .el-tabs__nav .el-tabs__item .el-icon-close,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped > .el-tabs__header .el-tabs__nav .el-tabs__item .el-icon-close
{
  font-size: inherit;
}
.el-icon-close{
  font-size: 22px;
}
.app .wrapper .content-box .content .el-tabs-scoped > .el-tabs__header .el-tabs__nav .el-tabs__item[class~=is-active] .el-icon-close,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped > .el-tabs__header .el-tabs__nav .el-tabs__item[class~=is-active] .el-icon-close
{
  font-size: inherit;
}

.app .wrapper .content-box .content .el-tabs-scoped > .el-tabs__content,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped > .el-tabs__content
{
  height:calc(100% - 40px);
}

.app .wrapper .content-box .content .el-tabs-scoped > .el-tabs__content .el-tab-pane,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped > .el-tabs__content .el-tab-pane
{
  height: 100%;
}
