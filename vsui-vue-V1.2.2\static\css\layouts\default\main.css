@import url("home.css");
/*********************************以下为框架整体布局样式控制，工作区布局请自行在页面中定义自己的样式********************************************/

html,
body,
.app,
.wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* min-width: 1200px; */
  font-size: 14px;
  margin: 0px;
  padding: 0px;
  font-weight:400;
}


/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  border-radius: 2px;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  border-radius: 2px;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 7px;
}
::-webkit-scrollbar-thumb:hover {
  border-radius:10px;
}
::-webkit-scrollbar-button:horizontal:decrement {
  /*当鼠标在垂直滚动条下面的按钮上的状态*/
  width: 7px;
  height: 7px;
  border-radius:2px;
}

::-webkit-scrollbar-button:vertical:decrement {
  /*当鼠标在水平滚动条下面的按钮上的状态*/
  width: 7px;
  height: 7px;
  border-radius:7px;
}

::-webkit-scrollbar-button:horizontal:decrement:hover {
  /*当鼠标在水平滚动条下面的按钮上的状态*/
  width: 7px;
  height: 7px;
}
::-webkit-scrollbar-button:vertical:decrement:hover {
  /*当鼠标在垂直滚动条下面的按钮上的状态*/
  width: 7px;
  height: 7px;
}

.app {

  font-family: "microsoft yahei", "Helvetica Neue", Helvetica, arial, sans-serif,
    PingFang SC, STHeiTi;
}






/**********************************以上为框架整体布局样式控制，工作区布局请自行在页面中定义自己的样式*********************************************/


.clearfix {
  overflow: hidden;
  clear: both;
}
.clearfix:after {
  content: "";
  display: block;
  clear: both;
}
.lf {
  float: left;
}
.rf {
  float: right;
}
.el-button [class*="fa-"] + span {
  margin-left: 5px;
}


.positionR{
  position: relative;
}
.positionA{
  position: absolute;
}
.hide
{
  display: none;
}
.show
{
  display: block;
}
.pd8 {
  padding: 8px;
}
