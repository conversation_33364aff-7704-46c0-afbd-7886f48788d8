/***
 *  by cuiliang on 20210901
 *  本文件用于声明接口访问地址，
 *  runtimeCfg.rest_base_path用于声明接口访问基地址，禁止直接修改本文件内的BasePath，
 *  如需求该请在static\js\runtime\config.js中查找window.__vsui_runtime_config__.__rest_base_path__变量并修改其值
 *  RestFulAPI.Interface.module1.PrefixPath 用于定义接口前缀（单应用模式）或服务前缀（微服务模式），
 *  在需要nginx代理的情况下，如应用为单应用，则直接代理window.__vsui_runtime_config__.__rest_base_path__的值到应用后端地址即可
 *                         如应用为微服务，则使用window.__vsui_runtime_config__.__rest_base_path__+RestFulAPI.Interface.module1.PrefixPath作为nginx的代理地址到相应服务的地址即可
 *  建议：
 *  1：使用示例1或示例2的接口定义方式，不建议示例3的接口定义方式，因为3不具备目录弹性，在做代理时难以附加利用
 *  2：示例1与示例2的选择取决于接口的变化性与共存性
 *  3：框架默认提供window.__vsui_runtime_config__.__rest_base_path__作为接口基路径，禁止声明多个如__rest_base_path1__、__rest_base_path2__的全局变量，
 *     如需要代理到不同服务器，请在链接中增加对RestFulAPI.Interface.module.PrefixPath的使用（如示例1或示例2），便于代理时区分不同的代理地址
 * 
 * 
 * 
 */

import { runtimeCfg } from "../core";


const BasePath=runtimeCfg.rest_base_path;

export default {
    RestFulAPI:{
        Interface:{
            /**示例1 */
            module1:{
                PrefixPath:"接口前缀，如'module1'或'module1/child'",
                Version:"版本号，如'v1.0'",
                rest1:()=>BasePath+"/"+this.PrefixPath+"/"+this.Version+"/rest1",
                rest2:()=>BasePath+"/"+this.PrefixPath+"/"+this.Version+"/rest2",
            },
            /**示例2 */
            module1:{
                PrefixPath:"接口前缀，如'module1'或'module1/child'",
                Version:"版本号，如'v1.0'",
                rest1:()=>BasePath+"/"+this.PrefixPath+"/rest1",
                rest2:()=>BasePath+"/"+this.PrefixPath+"/rest2",
            },
            /**示例3 */
            module2:{
                PrefixPath:"module2",
                Version:"v1.0",
                rest1:()=>BasePath+"/rest1",
            },
            

        }
    }
}