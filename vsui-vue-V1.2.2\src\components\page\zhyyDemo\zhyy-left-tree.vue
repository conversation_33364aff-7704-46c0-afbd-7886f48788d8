<template>
  <div class="orgTree">
    <div class="treeClass tree-container">
      <el-tree
        class="tree"
        ref="orgTreeRef"
        node-key="id"
        :props="defaultProps"
        :default-expand-all="false"
        :expand-on-click-node="false"
        :current-node-key="currentNodekey"
        @node-click="handleNodeClick"
        @node-expand="handleNodeClick1"
        @node-collapse="handleNodeClose"
        :default-expanded-keys="defaultKeys"
        highlight-current
        :data="data"
      >
        <!-- :load="loadNode"  :lazy="true"-->
        <!-- 添加图标 -->
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <span :dataType="data.type">
            <i class="iconfont" :class="[data.icon, 'tree-icon']" />
            {{ node.label }}
          </span>
        </span>
      </el-tree>
    </div>
  </div>
</template>
<script>

export default {
  props: {
    topOrgId: {
      type: String,
      default: "-",
    },
  },
  data() {
    return {
      menuList: [],
      defaultProps: {
        children: "children",
        label: "text",
        id: "id",
        parentId: "parentId",
        icon: "icon",
        isLeaf: "leaf",
      },
      data: [
        {
          id: "11",
          text: "某某组织1",
          icon:"icon-wenjian1",
          children: [
            {
              id: "221",
              text: "某某子组织1",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            }
          ],
        },
        {
          id: "12",
          text: "某某组织2",
          icon:"icon-wenjian1",
          children: [
            {
              id: "221",
              text: "某某子组织1",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            },
            {
              id: "222",
              text: "某某子组织2",
              icon:"icon-wendang",
            }
          ],
        },
      ],
      currentNodekey: this.topOrgId, //默认选中的节点树
      node_had: "",
      resolve_had: "",
      defaultKeys: [this.topOrgId], //默认展开父节点
    };
  },
  mounted() {
    //this.loadData();

  },
  methods: {
    /**
     * @description: 加载数据，先同步加载字典数据，再查询列表数据
     * @param {*}
     * @return {*}
     */
    loadData() {
      // let params = {
      //   topOrgId: this.topOrgId
      // };
      // API.queryChildOrgTreeList(params).then(res=>{
      //   //console.log(JSON.stringify(res));
      //   this.loading = false;
      //   this.data = res.rows;
        
      // });

    },

    handleNodeClick(data) {
      console.log("data", data);
      this.currentNodekey = data.id;
      this.$emit("checkedNode", this.currentNodekey);

    },
    
    // -节点图标---------------------------------------------------------------------------
    handleNodeClick1(data) {
      //data.icon = "icon-wenjianjia";
    },
    handleNodeClose(data) {
      //data.icon = "icon-wenjian1";
    },
  },
};
</script>
<style scoped>
.orgTree {
  position: absolute;
  top: 0px;
  bottom: 0;
  width: 220px;
  padding: 1px;
}
.organization {
    background-color: #2384B7;
    height: 32px;
    line-height: 32px;
    color: #fff;
    text-align: left;
    padding-left: 10px;
    
  }
  .treeClass {
    position: absolute;
    top: 8px;
    left: 6px;
    bottom: 0;
    width: 218px;
    overflow: auto;
    height: calc(100vh - 148px);
    display: flex;
  }
::v-deep .el-tree{
  width:220px!important;
}
</style>
