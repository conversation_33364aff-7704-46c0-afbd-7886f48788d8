<template>
    <div id="app" class="app"
    element-loading-text="加载中，请稍候..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.4)"
    element-loading-fullscreen="true"
    element-loading-lock="true"
    ><!--v-loading="showloading"-->
    <router-view></router-view>
  </div>
</template>

<script>
    import {  mapState } from 'vuex'
    import NProgress from 'nprogress'

    export default {
      name: 'App',
      data(){
        return {
          
        }
      },
      watch:{
        showloading:{
          handler: function(newval, oldval) {
            if (newval != oldval) {
              if(newval) {
                NProgress.start();}
              else{
                NProgress.done();
                }
            }
          }
        }
      },
      computed:{
        ...mapState({showloading:state=>state.APP.Loading}),
      },
      mounted() {
    
        NProgress.configure({     
          easing: 'ease',  // 动画方式    
          speed: 500,  // 递增进度条的速度    
          showSpinner: true, // 是否显示加载ico    
          trickleSpeed: 200, // 自动递增间隔    
          minimum: 0.3 // 初始化时的最小百分比
          })


      }
    }
    </script>


