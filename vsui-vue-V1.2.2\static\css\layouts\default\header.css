@import url("lefttopnavs.css");
/**************************头部样式开始*********************************/
.app .wrapper .header-box{
    position: relative;
    top:0px;
    width:100%;
  }
.app .wrapper .header-box .header {
    position: relative;
    box-sizing: border-box;
    height: 50px;
}
.app .wrapper .header-box.hidestate{
    position: absolute;
    top:-100px;
    z-index:1000;
}
.app .wrapper .header-box.hidestate .showheader
{
    position: absolute;
    line-height:10px;
    cursor: pointer;
    top:100px;
    right:10px;
}
.app .wrapper .header-box .header .left-top-menu {
    display: inline-block;
    width:50px;
    cursor: pointer;
    box-sizing: border-box;
    line-height: 50px;
}
.app .wrapper .header-box .header .left-top-menu:hover {

}
.app .wrapper .header-box .header .left-top-menu .left-top-menu-icon{
    width:100%;
    height:100%;
    font-size: 22px;
    text-align: center;
}


.app .wrapper .header-box .header .logo {
    cursor: default;
    width: 110px;
    height: 50px;
}

.app .wrapper .header-box .header .sysName {
    display: inline-block;
    line-height: 50px;
    cursor:default;
    font-size: 22px;
}
.app .wrapper .header-box .header .header-right {
    height:100%;
    align-items: center;
}

.app .wrapper .header-box .header .header-right >div {
    width:50px;
    line-height: 50px;
    text-align: center;
    margin: auto;
    cursor: pointer;
    box-sizing: border-box;
}
.app .wrapper .header-box .header .header-right >div:hover {

}
.app .wrapper .header-box .header .header-right .searchinfo
{
    width:auto;
    min-width:50px;
    padding: 0 15px 0 15px;
}
.app .wrapper .header-box .header .header-right .searchinfo .searchinput
{
    opacity: 0;
    width:0px;
    display: inline-block;
}
.app .wrapper .header-box .header .header-right .searchinfo .searchinput input.el-input__inner
{
    padding: 0;
    height:30px;
}
.app .wrapper .header-box .header .header-right .searchinfo .searchinput button.searchbtn{
    width: 0px;
    display: none;
}
.app .wrapper .header-box .header .header-right .searchinfo:hover>i{
    display: none;
}
.app .wrapper .header-box .header .header-right .searchinfo:hover .searchinput
{
    display:inline-table;
    opacity: 1;
    width:200px;
    border-radius: 0px;
}
.app .wrapper .header-box .header .header-right .searchinfo:hover .searchinput input.el-input__inner{
    padding: 0 15px;  
}
.app .wrapper .header-box .header .header-right .searchinfo:hover .searchinput .el-input-group__append{
    padding: 0 15px;
}
.app .wrapper .header-box .header .header-right .searchinfo:hover .searchinput .el-input-group__append button.searchbtn{
    width: 30px;
    padding: unset;  
    display: inline-block;
}
.app .wrapper .header-box .header .header-right .timeinfo {
    width:auto;
    padding: 0 15px 0 15px;
}
.app .wrapper .header-box .header .header-right .userinfo {
    width:auto;
    padding: 0 15px 0 15px;
}
.app .wrapper .header-box .header .header-right .hideheader {
    
}
.app .wrapper .header-box .header .header-right .hidingheader{
    position: fixed;
    color:#000;
    top:0px;
    right:1800px;
}
.app .wrapper .header-box .header .header-right .fullscreen {
    
}
.app .wrapper .header-box .header .header-right .themeswitch {
    
}
.app .wrapper .header-box .header .header-right .messagehint {
    
}
.app .wrapper .header-box .header .header-right .messagehint .el-badge {
    position: relative;
    vertical-align: unset;
    display: inline-block;
    height:100%;
}
.app .wrapper .header-box .header .header-right .messagehint .el-badge .el-badge__content.is-fixed{
    position: absolute;
    top: 15px;
}
.app .wrapper .header-box .header .header-right .setting{

}
.app .wrapper .header-box .header .header-right .logout {
    
}

/**********************上部通栏导航***************************/
.app .wrapper .header-box	.top-navs {
    height: 50px;
    line-height: 50px;
    position: relative;
    box-sizing: border-box; 
}

@media (max-width:1160px){
    .app .wrapper .header-box .header .header-right .searchinfo,
    .app .wrapper .header-box .header .header-right .timeinfo,
    .app .wrapper .header-box .header .header-right .userinfo,
    .app .wrapper .header-box .header .header-right .hideheader,
    .app .wrapper .header-box .header .header-right .fullscreen,
    .app .wrapper .header-box .header .header-right .themeswitch,
    .app .wrapper .header-box .header .header-right .messagehint,
    .app .wrapper .header-box .header .header-right .setting
    {
      display: none;
    }
}

.app .wrapper .header-box	.top-navs .el-menu-item-home
{
    width: 50px;
    line-height: 50px;
    text-align: center;
    cursor: pointer;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 22px;
}
.app .wrapper .header-box .top-navs .el-menu-item-home:hover
{
    
}

.app .wrapper .header-box .top-navs .el-menu-scoped
{
    display: flex;
    line-height: 100%;
    border:0px;
        
    margin-right: 0px;
    box-sizing: border-box;          
}

.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped
{
    height: 100%;
    line-height: 100%;
    border-radius: 0;
    border: none;
    padding: 0;
    box-sizing: border-box;
} 

.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped:hover
{
}
.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active]
{
    font-weight:bold;
}
    
.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped .el-menu-item-span-scoped
{
    display:block;
    height:100%;
    line-height:47px;
    font-size: 16px;
    margin: 0px 20px;
}

/*************************左上角导航栏**********************************/
.app .wrapper .header-box .left-navs{
    position: fixed;
    display: block;
    overflow-y: auto;
    top:50px;
    height: -moz-calc(100% - 80px);/*减顶部50px和底部30px*/
    height: -webkit-calc(100% - 80px);
    height: calc(100% - 80px);
    border: 0px;
    box-sizing: border-box;
    z-index:10000;
    left:0px;
    width:300px;
}
.app .wrapper .header-box .left-navs_collapse
{
    left:-300px;
    border: 0px;

}
.app .wrapper .header-box .themes-sel
{
    position: fixed;
    display: block;
    box-sizing:content-box;
    overflow-y: auto;
    z-index: 10000;
    right:  -1px;
    width: 200px;
    min-height:70px;
    max-height: 210px;
}
.app .wrapper .header-box .themes-sel .theme_item
{
    box-sizing: border-box;
    width:50px;
    height:70px;
    cursor: pointer;
}
.app .wrapper .header-box .themes-sel .theme_item .imgBox
{
    width:50px;
    height:50px;  
    line-height:50px;
    text-align: center; 
}
.app .wrapper .header-box .themes-sel .theme_item .imgBox img
{
    width:40px;
    height:40px;
    margin: 5px;
}

.app .wrapper .header-box .themes-sel .theme_item .txtBox
{
    text-align: center;
    width:100%;
}
.app .wrapper .header-box .themes-sel_collapse
{
    right:-201px;
    border: 0px;
}
/**************************头部样式完毕*********************************/