@import url("vue-components-drag-panel.css");
@import url("vue-components-page-loader.css");
@import url("about-me-index.css");




.app .wrapper .content-box .content .el-card-scoped .el-card__body,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__body
{
    color:#000;
    background-color: transparent;
}

.app .wrapper .content-box .content .el-card-scoped .el-card__body .el-container .el-main-scoped,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__body .el-container .el-main-scoped 
{
    color:#333333;
}

.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__body .el-container .pager  .el-pagination-scoped .el-pagination__total
 {
     color:#333333;
 }
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__body .el-container .pager  .el-pagination-scoped .el-pagination__jump
 {
    color:#333333;
 }

 .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped .el-tabs__header
 {
     
 }