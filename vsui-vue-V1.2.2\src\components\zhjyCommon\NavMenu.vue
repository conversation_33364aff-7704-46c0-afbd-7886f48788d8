<template>
  <el-submenu v-if="module.children && module.children.length > 0"
              :key="module.resId"
              :index="module.resId"
              class="el-menu-item-scoped">
    <template slot="title" class="el-submenu__title" taget="li">
      <i v-if="module.iconClass" :class="module.iconClass" :style="{'margin-left': 15 * (module.resFloorNum-1)+'px'}"></i>
      <span class="el-submenu-item-title-scoped" v-html="module.resName"></span>
    </template>
    <template v-for="(menu,ind) in module.children">
      <el-menu-item :key="menu.resId" :index="menu.resId" class="el-menu-item-scoped"
                    v-if="!menu.children || menu.children.length === 0">
        <i v-if="menu.iconClass" :class="menu.iconClass" :style="{'margin-left': 15 * (menu.resFloorNum-1)+'px'}"></i>
        <span class="el-submenu-item-title-scoped" v-html="menu.resName"></span>
      </el-menu-item>
      <NavMenu :module="menu" v-else :key="ind"></NavMenu>
    </template>
  </el-submenu>
</template>


<script scoped>
  import NavMenu from "./NavMenu"

  export default {
    name: 'NavMenu',
    components: {
      NavMenu
    },
    data() {
      return {
        titleLevel:0,
      };
    },
    props: {
      module: {
        type: Object,
        default: {}
      },
    },
    methods: {},
    computed: {},
    watch: {},
    created() {

    },
    mounted() {
    },
  };
</script>
<style scoped>


</style>
