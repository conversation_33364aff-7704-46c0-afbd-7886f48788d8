<template>
    <div class="wrap" style="height:100%;background-color:white">
        <div class="header">
            <div class="title">
                <div class="mbmc">模板名称</div>
                <div class="item">web</div>
            </div>
            <el-button @click="closePage()" size="small" type="primary">返回</el-button>
        </div>
        <div class="con">
            <div style="width:100%;text-align: center;">
                <el-image style="margin-left: 15px" :src="url">
                </el-image>
            </div>
            
            <div class="con-x">
                <div class="down">
                    <div class="icon"></div>
                    <p>下载源文件(文件类型)</p>
                </div>
                <div class="zp">
                    <div class="xinxi">
                        <div class="icon2"></div>
                        <p>设计人：李华</p>
                        <p>已有作品：20</p>
                    </div>
                    <div class="line"></div>

                    <div class="zuopin">
                        <div class="block">
                            <span class="demonstration">其他作品推荐</span>
                            <el-carousel height="180px">
                                <el-carousel-item v-for="item in 4" :key="item">
                                    <h3 class="small">

                                    </h3>
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                    </div>


                </div>
                <div class="xq">
                    <div class="neirong">
                        <div class="item">web</div>
                        <div class="tit-mc">胜利油田大监督信息平台首页样式</div>
                    </div>
                    <div class="neirong">
                        <div class="mc">尺寸/分辨率：1920px*1080px</div>
                    </div>
                    <div class="neirong">
                        <div class="mc">文件大小：1.2M</div>
                    </div>
                    <div class="neirong">
                        <div class="mc">文件格式：PPT</div>
                    </div>
                    <div class="neirong">
                        <div class="mc">页数：1</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
export default {
    props:{
        url: String, 
    },
    components: {

    },
    data() {
        return {

        }
    },
    methods: {
        closePage() {
            this.$emit("closeOnlineDialog");
        }
    },
    created() {

    }

}
</script>

<style scoped>
.el-button {
    margin-right: 30px;
    background-color: #0076F6 !important;
    border-radius: 15px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
}

.mbmc {
    margin: 10px;
}

.item {
    margin: 10px;
    width: 50px;
    height: 24px;
    border-radius: 12px;
    background-color: #e2ecf6;
    color: #1f6eb7;
    display: flex;
    justify-content: center;
    align-items: center;
}

.con {
    display: flex;
    justify-content: left;
    align-items: center;
}


.con-x {
    width: 480px;
    height: 648px;
    /* background-color: #e2ecf6; */
    margin-left: 15px;
}

.down {
    width: 480px;
    height: 80px;
    margin: 5px 0;
    border: #b7b7b7 1px solid;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.icon {
    width: 20px;
    height: 20px;
    background: url(./../../../../static/img/下载.png) no-repeat;
    background-size: 100% 100%;
    margin: 0 10px;
}

.icon2 {
    width: 40px;
    height: 40px;
    background: url(./../../../../static/img/admin.png) no-repeat;
    background-size: 100% 100%;
    margin: 0 10px;
}

p {
    font-size: 16px;
}

.zp {
    width: 480px;
    height: 320px;
    margin: 10px 0;
    border: #b7b7b7 1px solid;
    border-radius: 4px;
}

.xinxi {
    width: 480px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.line {
    width: 480px;
    height: 1px;
    background-color: #b7b7b7;
}

.el-carousel__item h3 {
    color: #475669;
    font-size: 16px;
    opacity: 0.75;
    line-height: 150px;
    margin: 0;
}

.demonstration {
    font-size: 16px;
    margin: 10px;
    color: #888888;
}
.block {
    margin: 10px;
}
.el-carousel {
    margin: 10px;
}
.el-carousel__item:nth-child(2n) {
    background-color: #9fc1e9;
}

.el-carousel__item:nth-child(2n+1) {
    background-color: #c6d8ee;
}

.xq {
    width: 480px;
    height: 210px;
    margin: 10px 0;
    border: #b7b7b7 1px solid;
    border-radius: 4px;
}

.neirong {
    display: flex;
    justify-content: left;
    align-items: center;
}

.tit-mc {
    margin: 10px 0px;
    font-size: 14px;
}

.mc {
    margin: 10px 20px;
    font-size: 14px;
}
</style>