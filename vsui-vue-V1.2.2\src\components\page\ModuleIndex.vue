<template>

    <el-container class="el-container-scoped">
      <el-aside class="el-aside-scoped">
        <v-sidebar></v-sidebar>
      </el-aside>
      <el-main ref="main" class="el-main-scoped">
        <router-view></router-view>
      </el-main>
    </el-container>  
</template>
<script>
import _class from "@/lib/Class.js";
import vSidebar from "../common/Sidebar.vue";
export default {
  components: {
    vSidebar
  },
  data() {
    return {
     
    };
  },
  computed: {
   ...mapState({userInfo:state=>state.APP.userInfo}),
  },
  watch:{
    $route:{
      deep:true,
      handle(newval,oldval){
        
          _class.addClass("el-main-scoped-load",this.$refs.main.$el)
          _class.removeClass("el-main-scoped",this.$refs.main.$el)
        
        this.$nextTick(()=>{
            _class.removeClass("el-main-scoped-load",this.$refs.main.$el)
            _class.addClass("el-main-scoped",this.$refs.main.$el)
	        });
      }
    }
  },
  };
</script>

  