 /***
 *  @author:cuiliang 
 *  @email: <EMAIL>
 *  @date：20210324
 *  @version:V1.2.0
 *  @description:
 *  该模块适合全局一次引入，请将该模块在main.js中引入，
 *  该模块用于禁用掉正式环境下的console.log输出
 *  @link  无
 * 
 * 
 * 
 */ 

 


 let console_log = window['console']?.['log']||window.console?.log;

 export default {
    
    open(){
        if(window.console?.log){
            window.console.log = console_log;
        }
        else if(window['console']?.['log']){
            window['console']['log'] = console_log;
        }
        else{
            return;
        }
        
    },
    close(){
        if(window.console?.log){
            window.console.log = ()=>{};
        }
        else if(window['console']?.['log']){
            window['console']['log'] = ()=>{}
        }
        else{
            return;
        }
        
    }
}
 

//process.env.NODE_ENV === 'production'?window.console.log=()=>{}:{};