<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="31" height="32" viewBox="0 0 31 32">
  <defs>
    <style>
      .cls-1 {
        stroke: #fff;
        stroke-width: 1px;
        fill-rule: evenodd;
        fill: url(#linear-gradient);
        filter: url(#filter);
      }
    </style>
    <linearGradient id="linear-gradient" x1="492.922" y1="635.438" x2="492.922" y2="618.25" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#80a5f6"/>
      <stop offset="0.01" stop-color="#80a5f6"/>
      <stop offset="1" stop-color="#0e31d1"/>
    </linearGradient>
    <filter id="filter" x="481" y="616" width="31" height="32" filterUnits="userSpaceOnUse">
      <feOffset result="offset" dx="2.536" dy="5.438" in="SourceAlpha"/>
      <feGaussianBlur result="blur" stdDeviation="2.646"/>
      <feFlood result="flood" flood-color="#0e2fd1" flood-opacity="0.3"/>
      <feComposite result="composite" operator="in" in2="blur"/>
      <feBlend result="blend" in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(-481 -616)" style="fill: url(#linear-gradient); filter: url(#filter)">
    <path id="形状_2" data-name="形状 2" class="cls-1" d="M492.751,635.429l-7.108-6.4s-1.321-3.355,2.37-2.327c-0.034.032,1.776,1.745,1.776,1.745v-9.306s1.218-2.026,2.962,0c-0.033.031,0,4.071,0,4.071a1.429,1.429,0,0,1,2.369,0c-0.033.115,0,1.164,0,1.164s1.557-1.943,2.962,0a0.974,0.974,0,0,0,0,.581,1.46,1.46,0,0,1,2.369,0c-0.033-.051,0,7.562,0,7.562s-0.982,2.212-2.961,2.327C497.456,634.879,492.751,635.429,492.751,635.429Z" style="stroke: inherit; filter: none; fill: inherit"/>
  </g>
  <use transform="translate(-481 -616)" xlink:href="#形状_2" style="stroke: #fff; filter: none; fill: none"/>
</svg>
