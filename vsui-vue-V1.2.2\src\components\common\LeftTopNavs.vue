<template>

  <div>
    <!-- <div @click="keyOfButton" style="cursor:pointer;font-size:30px;line-height:30px;background:#4a5064;"><i class="el-icon-more" style="color:#aeb9c2;"></i></div> -->
    <div class="lefttop-navs">
      <el-menu class="el-menu-scoped"  @select='handleSelect' unique-opened  @open="stopPropagation" @close="stopPropagation">
        <!-- unique-opened-->
        <template v-for="(module) in modules">
          <el-submenu  v-if="module.children && module.children.length > 0" 
            :key="module.resId" :index="module.resId"  class="el-menu-item-scoped">
            <template slot="title" class="el-submenu__title" taget="li">
              <i v-if="module.iconClass" :class="module.iconClass" :style="module.iconColor!=''?{'color':module.iconColor}:''" />
              <span class="el-submenu-item-title-scoped" >{{module.resName}}</span>
            </template>
            <el-tree :data="module.children" :node-key="module.resId" :props="{label:'resName',children: 'children'}" accordion highlight-current="false" @node-expand="stopPropagation" @node-collapse="stopPropagation" expand-on-click-node="false" @node-click="tree_node_click" class="el-tree-scoped">
              <span class="el-tree-node-scoped" slot-scope="{ node, data }">
                <i v-if="data.iconClass" :class="data.iconClass" :style="data.iconColor!=''?{'color':data.iconColor}:''" ></i>
                <span class="custom-tree-node" >{{data.resName}}</span>
              </span>
            </el-tree>
          </el-submenu>
          <el-menu-item v-else 
          :key="module.resId" :index="module.resId"  class="el-menu-item-scoped">
            <i v-if="module.iconClass" :class="module.iconClass" :style="module.iconColor!=''?{'color':module.iconColor}:''" ></i>
            
            <span class="el-submenu-item-title-scoped" >{{module.resName}}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
  </div>
</template>



<script scoped>
import {getResPvalueByResId} from "../../lib/comFun"
export default {
  data() {
    return {
      index:1
    };
  },
  props: { modules: { default: [] } },
  methods: {
    
    handleSelect(resId, indexPath) {
      let res=getResPvalueByResId(resId)
      if(res&&res.resPvalue!="") 
      {
        this.$emit("beforemoduleclick",res.resPvalue); 
        this.$router.push({path:res.resPvalue});
        this.$emit("moduleclicked",res.resPvalue);
        console.log("点击"+res.resPvalue, indexPath);   
      }
      event.stopPropagation();
    },
    tree_node_click(module,node,node1) {
      if(module.resId&&module.resId!=""&&module.resPvalue!="") 
      {
        //此处调用全局事件总线，触发事件，区别于Sidebar.vue中父子组件事件触发
        this.$EventBus.$emit("beforemoduleclick",module.resPvalue); 
        this.$router.push({path:module.resPvalue});
        this.$EventBus.$emit("moduleclicked",module.resPvalue);
        console.log("点击"+module.resPvalue)
      }
      event.stopPropagation();
    },
	stopPropagation(){
      event.stopPropagation();
    },
    initMenuTree() {
      this.modules;
    }
  },
  mounted() {
    this.initMenuTree();
  },
  updated() {
  },
};
</script>