
import {Uri} from "@vsui/lib-jsext";
import  {auth} from "../assets/core/index";
 /***
 *  @author:cuiliang 
 *  @email: <EMAIL>
 *  @date：20191024
 *  @version:V1.2.0
 *  @description:
 *  该模块开放函数在需要的地方引入即可，禁止全局引入，因为全局引入后您仍需要在每个使用的模块中单独引入
 *  该模块为浏览器全屏模式与正常模式开关函数，内置状态机制，初始为正常模式，有如下功能：
 *  1：covertDataToCascaderTree convertDataToModuleTree函数的升级版，不仅可以将模块数据变成树状的结构，
 *  并且在每个模块数据上增加了parentIds属性，该属性为当前模块所有上层节点组成的数组（不含当前模块的id，元素为由顶层节点开始到当前节点的父节点结束组成的数组）
 *  2：getParentIdArr 在模块列表数据中查找某个id的模块的上级模块组成的数组，返回值为从顶层模块开始到当前id模块父模块结束组成的数组
 * 
 *  @example:
 *  无
 * 
 *  @interface:
 *  covertDataToCascaderTree(json)
 *  getParentIdArr(string,json)
 * 
 * 
 */ 




/**
 * convertDataToModuleTree函数的升级版，不仅可以将模块数据变成树状的结构，
 * 并且在每个模块数据上增加了parentIds属性，该属性为当前模块所有上层节点组成的数组（不含当前模块的id，元素为由顶层节点开始到当前节点的父节点结束组成的数组）
 * @param {*} data 同convertDataToModuleTree的参数data
 */
function covertDataToCascaderTree(data)
{
	var root = [], o = {};
	function add(arr, topic, parenttopic) {
		var obj = JSON.parse(JSON.stringify(topic));
		obj.children = [];
		if (!obj.parentIds) obj.parentIds = [];
		if (parenttopic) { obj.parentIds = [].concat(parenttopic.parentIds); obj.parentIds.push(parenttopic.id + ""); }
		o[obj.id] = obj;
		obj.parent=parenttopic;
		arr.push(obj);
	}
	if (data instanceof Array) {
		data.forEach(item => {
		if (o[item.pid]) {
			add(o[item.pid].children, item, o[item.pid]);
		} else {
			add(root, item, undefined);
		}
		});
	}
	o = null;
	//使用递归删除children为空的属性
	function cleanChildren(__subject){
		if(__subject.children&&__subject.children.length==0)
		{
			delete __subject.children;
		}
		else
		{
			for(let j=0;j<__subject.children.length;j++)
			{
				cleanChildren(__subject.children[j])
			}
		}
		
	}
	
	for(let i=0;i<root.length;i++)
	{
		let _subject=root[i];
		cleanChildren(_subject)
	}
	
	return root;     
}

/**
 * 在模块列表数据中查找某个id的模块的上级模块组成的数组，返回值为从顶层模块开始到当前id模块父模块结束组成的数组
 * @param {*} id 要查找的模块id
 * @param {*} data 同convertDataToModuleTree的参数data
 */ 
function getParentIdArr(id,data){
	function foo(data) {
	  for(var k in data) {
	    line[data[k].id] = data[k];
	    if(data[k].children) {
	       foo(data[k].children);
	    }
	  }
	}
	function getParents(id) {
	    var res = [];
	    if(! line[id]) return res;
	    res.push(line[id].id)
	    return res.concat( getParents(line[id].pid) );
	}
	var line = {};
	foo(data)
	// console.log(line); //穿线
	var r = getParents(id);
	// console.log(r);
	// console.log(r.reverse(),r);
	return r.reverse()
}

/**
 * 根据资源resid获取资源描述对象
 * @param {object} resId 资源id
 */
function getResPvalueByResId(resId){
	let permissions=auth.instance.getPermission();
	if(permissions&&permissions.resList&&permissions.resList.length>0)
	{
	   for(let i=0;i<permissions.resList.length;i++)
	   {
		 if(permissions.resList[i].resId==resId) return permissions.resList[i];
	   }
	}
}

/**
 * 通过模块树型结构按照关键字查找
 * @param {array} moduleNode 从哪个模块的根节点开始查找，不可
 * @param {} key 
 * @param {*} value 
 */
function searchModulesTree(tree,key, value)
{
	tree=JSON.parse(JSON.stringify(tree))
	let searchTree=function (node, index, value, removeArr, replaceStr, regExp){
		// if( !node ) return;
		let children = node.children
		//针对非叶子节点，需要递归其children节点
		if(children && children.length > 0){
			let innderArr = []
			for(let i=0;i<children.length;i++){
				searchTree(children[i], i, value, innderArr, replaceStr, regExp)
			}
			//如果当前节点不满足搜索条件，则对其children不满足条件的节点执行删除操作
			if(node[key].indexOf(value) === -1){
				for(let j=innderArr.length-1; j>=0 ; j--){
					children.splice(innderArr[j], 1)
				}
				/*
				*children节点删除结束后，如果children length为0，
				*并且当前节点也不满足搜索条件，则当前节点也加入删除数组
				*/
				if(node.children.length === 0){
					removeArr.push(index)
				}
			}else{
				//当前节点非叶子节点，将满足条件内容做标记显示
				node[key] = node[key].replace(regExp, replaceStr)
			}
		}else{
			//叶子节点，直接进行匹配
			if(node[key].indexOf(value) === -1){
				removeArr.push(index)
			}else{
				//将满足条件内容做标记显示
				node[key] = node[key].replace(regExp, replaceStr)
			}
		}
	}
	//不满足搜索条件的待删除元素索引数组
	let removeArr = []
	// eslint-disable-next-line no-eval
	//replace时正则匹配，全局匹配 
	// eval('/' + value + '/g')
	const globalEval = eval;
	let regExp =  globalEval('/' + value + '/g')
	//满足条件的字符串匹配为以下内容，红色显示，可以根据自己需要调整以下字符串
	let replaceStr = `<span class='red'>${value}</span>`
	for(let i = 0; i< tree.length; i++){
		let node = tree[i]
		searchTree(node, i, value, removeArr, replaceStr, regExp)
	}
	//遍历删除不满足条件的节点
	for(let j=removeArr.length-1; j>=0 ;j--){
		tree.splice(removeArr[j], 1)
	}
	return tree;
}
/**
 * 对url生成正则匹配，如：/form_pinyin/{a}/fdfsd?s={b}&b={c}
 * 可生成对a,b,c参数任意值的正则匹配，主要用于mock生成对url的匹配
 * @param {uri} formatUrl 要生成mock正则匹配的url
 */
function convertFormatUrlToReg(formatUrl){
	if(formatUrl&&formatUrl!=""){
		let params=Uri.getJsonFromUrlParams(formatUrl);
		for(let param in params){
			params[param]=params[param].replace(/\{([\s\S]*)\}/,"([\\s\\S]*)")
			formatUrl=Uri.setParameter(param,params[param],formatUrl);
		}
		formatUrl = formatUrl.replace(/\{([\s\S]*)\}/,"([\\s\\S]*)").replace(/\//g,"\/").replace("?","\\?").replace(/\&/g,"\\&")
		return new RegExp(formatUrl)
	}
	return formatUrl;
}


export {
	covertDataToCascaderTree,
	getParentIdArr,
	getResPvalueByResId,
	searchModulesTree,
	convertFormatUrlToReg,
}