
/**********************左侧菜单*****************************/

.app .wrapper .header-box .left-navs .lefttop-navs  {
    background-color: transparent;
  }
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped {
    background-color: transparent;
    border-right: 0px solid #666666;
    color: #fff;
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-submenu__title {
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped{
}
  
  
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped,
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped[class~=is-opened]{
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped:hover
{
    background-color: transparent;
    color: #fff; 
}
/*********左侧菜单图标字体设置*************/
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped i{
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped:hover .el-submenu-item-title-scoped{
    color: #fff; 
}
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active]{
    background-color: transparent;
    color: #689DF4;
}
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-menu
{
    background: transparent ;
}

.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title{
    background: transparent;
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title i{
    color: #fff;
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover{
    background: transparent;
 }
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover i {
    color: #fff;
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu-item-title-scoped{
    color: #fff; 
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped {
    background: transparent;
}
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content
{
    color:#fff;
    background: transparent; 
}

.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content:hover{
    color: #fff !important;
    background-color: #689DF4 !important;
}
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node[class*="is-current"] > .el-tree-node__content {
    color: #689DF4 ;
}
  
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped i{
}
  
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped .custom-tree-node {
}