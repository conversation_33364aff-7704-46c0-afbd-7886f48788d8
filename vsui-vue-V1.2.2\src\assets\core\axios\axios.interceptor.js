
import store from '../store'
import {Message} from 'element-ui'

/**
 * 系统层拦截器，处于拦截器最外层，其特点是：会最后收到请求拦截，最早收到收到应答拦截
 */
const axiosInterceptor0={
    request:function(config)
    {
        store.dispatch("APP/Loading",true);
        console.log("axios-request:1")
        return config;
    },
    requestError:function(error)
    {
        store.dispatch("APP/Loading",false);
        throw error;
    },
    response:function(response)
    {
        store.dispatch("APP/Loading",false);
        console.log("axios-response:1")
        return response;
    },
    responseError:function(error)
    {
        store.dispatch("APP/Loading",false);
        throw error;
    },
}

/**
 * 应用层拦截器，处于拦截器最内层，其特点是：会最早收到请求拦截，最后收到应答拦截
 */
const axiosInterceptor2={
    request:function(config)
    {
        console.log("axios-request:3")
        return config;
    },
    requestError:function(error)
    {
        Message({message:"请求超时,请检查本地或服务器网络情况!", 
            type:"error", 
            duration:3000,
            showClose:false,
            customClass:"el-message--error-custom",
            dangerouslyUseHTMLString:true,
            onClose:function(){return}
        });
        throw error;
    },
    response:function(response)
    {
        console.log("axios-response:3")
        return response;
    },
    responseError:function(error)
    {
        /*Message({message:"已接收到返回，但数据加载失败,错误原因如下：<br/>"+error.message,
            type:"error", 
            duration:3000,
            showClose:false,
            customClass:"el-message--error-custom",
            dangerouslyUseHTMLString:true,
        });*/
        throw error;
    },
}

export {axiosInterceptor0,axiosInterceptor2};

