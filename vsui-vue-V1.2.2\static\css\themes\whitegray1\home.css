@import url("header.css");
@import url("footer.css");
@import url("dashboard.css");
@import url("sidebar.css");
@import url("container.css");

/**************************主内容区域***********************************/

.app .wrapper .content-box {
    background: transparent;
    height: calc(100% - 130px);
  }
  .app .wrapper .content-box-hiddenHeader {
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition:all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    background: #f0f0f0;
  }
  
  .app .wrapper .content-box.hidestate
  {
    transition: all 0.2s ease-in-out;
  }
  .app .wrapper .content-box .content {
    background-color: #F2F2F2 ;
  }
  
  .app .wrapper .content-box .content .el-container-scoped {
    background: transparent;
  }
  
  /* 左侧导航样式 */
  .app .wrapper .content-box .content .el-container-scoped .el-aside-scoped {
    background-color: transparent;
  }
  
  /* 内容区 背景色 */
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped-load {
  
  }
  
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped {
    background-color:#F2F2F2;
  }

  /**

  begin 单面板工作区下的样式定义


  以下两条分别对应：
  适配上、下区域模式下（如dashboard页面）下方工作区布局
  适配上、下左右区域模式下（如vsui-vue/v1-1-0-index页面）下右方为单面板工作区布局

  */
  .app .wrapper .content-box .content .el-card-scoped,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped{
    background-color: transparent;
  }

  .app .wrapper .content-box .content .el-card-scoped .el-card__header,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__header {
    
    border-bottom: 1px solid #cccccc ;
  }
  
  /*默认头部标题字体样式 */
  .app .wrapper .content-box .content .el-card-scoped .el-card__header .title_bar,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__header .title_bar{
    color:#333333;
    font-size: 14px;
    font-weight: normal;
  }
  .app .wrapper .content-box .content .el-card-scoped .el-card__header .el-button-scoped,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped .el-card__header .el-button-scoped{
    border: 1px solid #dddddd;
    color:#333333;
  }
  /**end 单面板情况下的样式定义*/


  /**

  begin 选项卡情况下的样式定义
  适配上、下左右区域模式下（如aboutme页面）下右方为选项卡工作区布局
  以下两条分别对应：
  适配上、下区域模式下（如dashboard页面）下方工作区布局
  适配上、下左右区域模式下（如vsui-vue/v1-1-0-index页面）下右方为单面板工作区布局

  */




  .app .wrapper .content-box .content .el-tabs-scoped,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped{
    background-color: transparent;
  }

  /**
  以下两条分别对应选项卡模式下的适配：
  适配上、下区域模式下（如dashboard页面）下方为选项卡时，面板头部布局样式
  适配上、下左右区域模式下（如aboutme页面）下右方为选项卡时，面板头部布局样式
  */
  .app .wrapper .content-box .content .el-tabs-scoped .el-tabs__header,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped .el-tabs__header
  {
    border-bottom: 1px solid #dddddd ;
    background-color: #ffffff;
  }

  .app .wrapper .content-box .content .el-tabs-scoped .el-tabs__header .el-tabs__nav ,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped .el-tabs__header .el-tabs__nav 
  {
    border: 1px solid #dddddd;
    border-left: none;
    border-bottom: none;
    border-top:none;
  }

  .app .wrapper .content-box .content .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item
  {
    color:#333333;
    border: 0px solid transparent;
    border-right: 1px solid #dddddd;
  }
  .app .wrapper .content-box .content .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item:last-child,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item:last-child
  {
    border-right:0px;
  }

  .app .wrapper .content-box .content .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item[class~=is-active],
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item[class~=is-active]
  {
    color:#2384B7;
  }

  .app .wrapper .content-box .content .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item .el-icon-close,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item .el-icon-close
  {
    color:inherit;
  }
  .app .wrapper .content-box .content .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item[class~=is-active] .el-icon-close,
  .app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-tabs-scoped .el-tabs__header .el-tabs__nav .el-tabs__item[class~=is-active] .el-icon-close
  {
    font-size: inherit;
    color:red;
  }

  



  