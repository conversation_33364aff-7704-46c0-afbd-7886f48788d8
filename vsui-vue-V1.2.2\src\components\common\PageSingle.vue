<template>

    <el-container class="el-container-scoped">
      <el-aside tag="div" class="el-aside-scoped" v-if="!hiddenSidebar"  style="width:auto;">
          <v-sidebar :modules="currentModule==null?[]:currentModule.children" @moduleclicked="moduleclicked"></v-sidebar>
      </el-aside>
      <el-main ref="workbench"  class="el-main-scoped">
        <!--<transition name="el-zoom-in-top">-->
            <router-view></router-view>
        <!--</transition>-->
      </el-main>
    </el-container>  
</template>
<script>
import vSidebar from "./Sidebar.vue";
import {mixin,runtimeCfg} from "../../assets/core/index";
export default {
  components: {
    vSidebar
  },
  mixins:[mixin],
  data() {
    return {
      hiddenSidebar:this.$route.query.hasOwnProperty("hidsid")?true:runtimeCfg.page_hid_sidebar,
    };
  },
  computed: {
    modulesTree : function(){return this.auth.instance.getModulesTree()},
    currentModuleResId : function(){return this.$route.matched[1].path},//this.$route.matched[1] 地址匹配的是路由的第二层，第一层为"/"
    currentModule : function(){
      if(!this.currentModuleResId) return null;
      for(let i=0;i<this.modulesTree.length&&this.modulesTree.length>0;i++)
      {
        if(this.modulesTree[i].resPvalue.indexOf(this.currentModuleResId+"")>-1)
        {
          return this.modulesTree[i];
        }
      }
      return null;
        
    },
  },
  methods:{
    /**
     * 侧边栏点击事件，本函数返回true，则自动使用路由跳转
     * 
     */
    moduleclicked(routerPath)
    {
      if(routerPath&&routerPath!=""&&this.$route.path!=routerPath) {
        console.log(`路由跳转：${routerPath}`)
      }
    }
  },
};
</script>
  