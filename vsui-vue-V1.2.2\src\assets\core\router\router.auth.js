
/***
 * Powered by @vsui/lib-vueauth4vseaf (版本请参照当前项目使用的组件版本)
 * 
 * Created in 20210429 by cuiliang 
 * 
 * 本文件为路由鉴权实现，提供了
 * 
 * 
 * 
 */

/**
 * 通过自定义函数的方式，验证用户对当前url地址是否有权访问，
 * 入口参数均为鉴权组件自动传入，来源自鉴权组件从后端读取的权限数据
 * 也可以再本文件头部导入自己的其他数据作为验证依据,在此函数中使用，
 * @param route to 要到的页面路由对象
 * @param String userName 当前登录的账号，用户未登录则为""
 * @param Object userPermission 当前账户的权限对象，此数据对象为服务器返回的前线数据，开发者如果不使用胜软权限中心时，可以自定义后端返回的权限数据，此处再访问路由时会将此数据作为参数自动传入
 * @return Boolean 返回是否具有权限进入页面，true：有权进入此路由（页面）；false:无权进入此路由（页面） 
 */
function customCheck(to,userName,userPermission){
    let result=userName!='';
    //todo 请在这里完成验证过程
    return result;
}
  
const autoCheck=true;

const withoutCheck=undefined;

export {
    customCheck,
    autoCheck,
    withoutCheck
}