<template>
    <div class="wrap" style="height:100%;background-color:white">
        <div class="wrap-header">
            <div class="wrap-left">
                <div v-on:click="clickTabs('index')" :class="this.pageLx=='index'?'item':'item2'">系统首页</div>
                <div v-on:click="clickTabs('screen')" :class="this.pageLx=='screen'?'item':'item2'">系统大屏</div>
                <div v-on:click="clickTabs('mobile')" :class="this.pageLx=='mobile'?'item':'item2'">移动端</div>
            </div>
        </div>
        <!-- 上传功能组件 -->
        <div class="fupload">
            <vsfileupload ref="childfilelist" :busId="busId"></vsfileupload>
        </div>

        <div class="con" style="overflow-y: auto;height:calc(100vh - 200px);">
            <div v-for="(item,index) in fileList" :key="index" style="display: inline-block">
                <div class="wrap-x">
                    <div class="center1">
                        <div class="rect">
                            <div class="rect-con"></div>
                            <div class="xxx" @click="show(item.url)">
                                <el-image style="width: 192px; height: 108px;" :src="item.url"></el-image>
                            </div>
                        </div>
                        <div style="text-align: center;height: 50px;line-height: 50px">
                            <span>{{item.name}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog  v-dialogDrag
            v-if="showOnline"
            title="在线打开"
            fullscreen
            :visible.sync="showOnline" 
            :before-close="handleCloseOnlineDialog" 
            :close-on-click-modal="false" >
            <showOnline :url="url" @closeOnlineDialog="handleCloseOnlineDialog"/>
        </el-dialog>
    </div>
</template>

<script>
//import imgViewxq from './imgViewxq.vue'
import showOnline from './online.vue'
import vsfileupload from '../../common/vsfileupload.vue'
export default {
    components: {
        showOnline,vsfileupload,
    },
    data() {
        return {
            busId: 'qwerqwqwerqwerqwqwer',//业务ID
            pageLx: "index",
            showOnline:false,
            fileList: [],
        }
    },
    methods: {
 
        initData(){
            if(this.pageLx == 'index'){
                this.fileList = [
                    {"url":"/static/img/pic.png","name":"胜利油田大监督信息平台"},
                    {"url":"/static/img/symb/rlzy.png","name":"胜利油田员工培训系统"},
                    {"url":"/static/img/symb/scpt.png","name":"中原油田市场管理系统"},
                    {"url":"/static/img/index/信访统计界面.png","name":"信访统计界面"},
                    {"url":"/static/img/index/村级信访-信访管理界面.png","name":"村级信访-信访管理界面"},
                    {"url":"/static/img/index/网格信息统计界面.png","name":"网格信息统计界面"},
                    {"url":"/static/img/index/收入福利统计界面.png","name":"收入福利统计界面"},
                    {"url":"/static/img/index/集体收入管理界面.png","name":"集体收入管理界面"},
                    {"url":"/static/img/index/发展党员预警界面.png","name":"发展党员预警界面"},
                    {"url":"/static/img/index/流动党员统计界面.png","name":"流动党员统计界面"},
                    {"url":"/static/img/index/民主决策统计界面.png","name":"民主决策统计界面"},
                    {"url":"/static/img/index/连通性分析.png","name":"连通性分析"},
                    {"url":"/static/img/index/注采平衡状况分析.png","name":"注采平衡状况分析"},
                    {"url":"/static/img/index/注采对应曲线.png","name":"注采对应曲线"},
                    {"url":"/static/img/index/压力分析.png","name":"压力分析"},
                    {"url":"/static/img/index/宏观工况分析.png","name":"宏观工况分析"},
                    {"url":"/static/img/index/井史可视化.png","name":"井史可视化"},
                    {"url":"/static/img/index/开发历程分析.png","name":"开发历程分析"},
                    {"url":"/static/img/index/采收率评价.png","name":"采收率评价"},
                    {"url":"/static/img/index/含水-存水率-水驱指数分析.png","name":"含水-存水率-水驱指数分析"},
                    {"url":"/static/img/index/劈产劈注方案创建.png","name":"劈产劈注方案创建"},
                    
                ];
            }else if(this.pageLx == 'screen'){
                this.fileList = [
                    {"url":"/static/img/dpzs/dpzs.png","name":"胜利油田市场运行管理系统大屏"},
                    {"url":"/static/img/screen/总体态势.png","name":"总体态势"},
                    {"url":"/static/img/screen/经济运行.png","name":"经济运行"},
                    {"url":"/static/img/screen/信用管理.png","name":"信用管理"},
                    {"url":"/static/img/screen/产业发展.png","name":"产业发展"},
                    {"url":"/static/img/screen/项目管理.png","name":"项目管理"},
                    {"url":"/static/img/screen/能源管控.png","name":"能源管控"},
                    {"url":"/static/img/screen/社会治理.png","name":"社会治理"},
                    {"url":"/static/img/screen/智慧社区.png","name":"智慧社区"},
                    {"url":"/static/img/screen/安保维稳.png","name":"安保维稳"},
                    {"url":"/static/img/screen/网格治理.png","name":"网格治理"},
                    {"url":"/static/img/screen/城市服务.png","name":"城市服务"},
                    {"url":"/static/img/screen/应急指挥.png","name":"应急指挥"},
                    {"url":"/static/img/screen/环保一张图.png","name":"环保一张图"},
                    {"url":"/static/img/screen/环保感知.png","name":"环保感知"},
                ];
            }else if(this.pageLx == 'mobile'){
                this.fileList = [
                    {"url":"/static/img/mobile/企业反馈情况查看.jpg","name":"企业反馈情况查看"},
                    {"url":"/static/img/mobile/检查记录查看.jpg","name":"检查记录查看"},
                    {"url":"/static/img/mobile/检查二维码出示.jpg","name":"检查二维码出示"},
                    {"url":"/static/img/mobile/巡查申请.jpg","name":"巡查申请"},
                    {"url":"/static/img/mobile/胜利油田人力资源员工服务平台.png","name":"胜利油田人力资源员工服务平台"},
                    {"url":"/static/img/mobile/政策解读.png","name":"政策解读"},
                    {"url":"/static/img/mobile/政策宣讲.png","name":"政策宣讲"},
                    {"url":"/static/img/mobile/胜利微课政策微视.png","name":"胜利微课政策微视"},
                    {"url":"/static/img/mobile/工作动态重点资讯.png","name":"工作动态重点资讯"},
                    {"url":"/static/img/mobile/留言板.png","name":"留言板"},
                    {"url":"/static/img/mobile/薪酬查询.png","name":"薪酬查询"},
                    {"url":"/static/img/mobile/技能鉴定.png","name":"技能鉴定"},
                    {"url":"/static/img/mobile/问题投诉咨询信箱.png","name":"问题投诉咨询信箱"},
                    {"url":"/static/img/mobile/预防化解.png","name":"预防化解"},
                    {"url":"/static/img/mobile/个人中心.png","name":"个人中心"},

                ];
            }
        },
        clickTabs(val) {
            this.pageLx = val;
            this.initData();
        },
        show(url){
            this.url = url;
            this.showOnline = true;
        },
        handleCloseOnlineDialog(){
            this.showOnline = false;
        },

    },
    created() {
    },
    mounted(){
        this.initData();
    }

}
</script>

<style scoped>
.el-button {
    margin-right: 30px;
    background-color: #0076F6 !important;
    border-radius: 15px;
}

.wrap-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
}

.wrap-left {
    width: 85vw;
    height: 8vh;
    display: flex;
    justify-content: left;
    align-items: center;
    font-size: 16px;

}

.item {
    margin-left: 30px;
    width: 82px;
    height: 36px;
    background-color: #e2ecf6;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 18px;
    color: #1f6eb7;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer
}
.item2 {
    margin-left: 30px;
    width: 82px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    color: #444;
    cursor: pointer
}

.item2:hover,.item:hover {
    background-color: #e2ecf6;
    border-radius: 18px;
}

.fupload {
    width: 125px;
    float: right;
}

.con {
    width: "100vw";
    height: "100vh";
    margin-top:40px;
    flex-wrap: wrap;
}
.center1 {
    width: 280px;
    height: 260px;
}

.top {
    width: 85vw;
    height: auto;
}
.wrap-con {
    width: 100%;
    justify-content: left;
    align-items: center;
    flex-wrap: wrap;
}
.wrap-x {
    width: 280px;
    height: 220px;
    margin: 20px 10px;
}
.rect {
    width: 240px;
    height: 190px;
    margin-left: 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}
.rect-con {
    background-color: #e2ecf6;
    width: 240px;
    height: 190px;
    border-radius: 4px;
    box-shadow: inset 0 -20px 30px rgba(47, 74, 99, 0.2);
    display: flex;
    justify-content: left;
    align-items: center;
}

.xxx {
    width: 192px;
    height: 108px;
    background-size: 100%, 100%;
    position: absolute;
    transition: all 0.5s;
}
.xxx:hover {
    transform: scale(1.5);
}

image {
    margin-left: 20px;
}
::v-deep .el-dialog {
    max-height: calc(100vh);
}
::v-deep .el-dialog__body {
    max-height: calc(100vh);
}
</style>