<template>
	<div>
		<el-upload
				name="files"
				:disabled="!editable"
				class="upload-demo"
				:action="uploadUrl"
				:accept="accept"
				:data="fileUploadData"
				:on-preview="handlePreview"
				:on-success="handleSuccessXgfj"
				:on-remove="handleRemove"
				:before-remove="beforeRemove"
				:file-list="fileList"
		>
			<el-button size="small" type="primary" v-if="editable" >点击上传</el-button>
		</el-upload>
	</div>
</template>
<style scoped>

</style>
<script>

	export default {
		props: {
			accept: String, //允许上传文件类型
			editable: { //是否可编辑，默认可编辑
				type: Boolean,
				default: true
			},
			busId: { //附件业务ID
				type: String,
				required: true,
			},
			ywlb: {  //业务类别，用于标识用同一id有不同业务类型的附件
				type: String,
				default: 'default'
			},
			busType: {  //配置文件中的业务类型
				type: String,
				default: 'file_kj'
			},
		},
		data() {
			return {
				uploadUrl: '/backend/vscomponent/fileupload/upload',
				fileUploadData: {
					busType: 'file_kj',
					busId: this.busId,
					standbyField0: this.ywlb,
				},
				//附件列表
				//fileList: [],
				//演示数据，接入后台删除
				fileList: []

			};
		},
		methods: {
			//单机文件名触发,下载
			handlePreview(file) {
				window.open("/backend/vscomponent/fileupload/download?id=" + file.id);
			},
			//加载附件信息
			handleSuccessXgfj(response, file, fileList) {
				if(!this.busId || this.busId == ''){
					return false;
				}
				this.fileList = [];
				this.axios({
					method: "post",
					url: "/backend/vscomponent/fileupload/list",
					data: {busId: this.busId, standbyField0: this.ywlb}
				}).then((response) => {
					if (response.data && response.data.data) {
						for (var j = 0; j < response.data.data.length; j++) {
							response.data.data[j].name = response.data.data[j].fileName;
							this.fileList.push(response.data.data[j]);
						}
					}
				}).catch((errror) => {
						this.$message.error("加载附件信息失败！");
					});
			},
			//附件删除事件
			handleRemove(file, fileList) {
				this.axios({
					method: "post",
					url: "/backend/vscomponent/fileupload/del",
					data: {id: file.id, delFlag: "1"},
					responseType: "json",
				}).then((response) => {
					if (response.data && response.data.meta && response.data.meta.success) {
						this.handleSuccessXgfj();
						this.$message({type: "success", message: "删除成功！"});
					}
				}).catch((errror) => {
					this.$message.error("删除失败！");
				});
			},
            //删除触发执行
            beforeRemove(file, fileList) {
                return this.$confirm("确定删除 " + file.name + "？");
            },
		},
		created: function () {
			if(this.busId != ''){
				//演示数据，暂时注释，接入后台开启
				//this.handleSuccessXgfj(); //加载附件
			}
		}
	}
</script>