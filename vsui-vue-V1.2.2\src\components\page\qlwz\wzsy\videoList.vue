<template>
    <div>
        <div>
            <iframe src="/static/header.html" ref="iframe" width="100%" scrolling="no" height="210px"
                style="border: none;"></iframe>
        </div>


        <div>
            <el-row>

                <el-input @keyup.enter.native="processSearch" style="width: 200px;" placeholder="请输入视频名称"
                    v-model="listQuery.fileName" clearable>
                </el-input>
                <el-button type="primary" icon="el-icon-search" style="margin-left:8px;"
                    @click="processSearch">查询</el-button>

            </el-row>
            <el-row>
                <el-col>
                    <div class="video-list" v-for="item in listData" :key="item.id">
                        <div class="video-name" @click="setVideo(item)">
                            {{ item.fileName }}
                        </div>
                        <div class="publish-time">
                            {{ item.publishTime }}
                        </div>
                    </div>
                </el-col>
            </el-row>
            <div class="qlwz-list-paginationArea">
                <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :page-sizes="[10, 20, 30, 50]" :page-size="listQuery.pageSize" :current-page="listQuery.pageIndex"
                    layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
            </div>
        </div>
        <div style="width: 1200px; margin: auto;">
            <iframe src="/static/footer.html" ref="iframe" width="100%" scrolling="no" style="border: none;"></iframe>
        </div>
    </div>

</template>
<script>
export default {
    watch: {
        $route: {
            handler(oldVal) {
                this.loadListData();
            },
            deep: true
        },
    },
    data() {
        return {
            itemData: {},
            total: null,
            listData: [],
            listQuery: {
                pageIndex: 1,
                pageSize: 10,
                fileName: '',//视频名称
            },
            currentVideoId: '',//接受查到的视频的主键id
            bigPicture: null, // 当前放大的图片索引
            playerOptions: {
                playbackRates: [0.7, 1.0, 1.5, 2.0], // 播放速度
                autoplay: false, // 如果true,浏览器准备好时开始回放。
                muted: false, // 默认情况下将会消除任何音频。
                loop: false, // 导致视频一结束就重新开始。
                preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
                language: 'zh-CN',
                aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
                fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
                sources: [{
                    type: 'video/mp4', // 类型
                    src: '' // url地址
                }],
                poster: '', // 封面地址
                notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
            },
        }
    },
    mounted() {
        this.listLoading();
    },
    methods: {
        setVideo(item) {
            this.axios({
                method: 'post',
                url: '/backend/qlwz/queryVideo',
                data: item,
            }).then(response => {
                if (response.data.data) {
                    this.currentVideoId = response.data.data.id;
                    item.currentVideoId = this.currentVideoId;
                    localStorage.setItem('itemData', JSON.stringify(item));
                    const routeData = this.$router.resolve({
                        name: 'detail',
                        params: this.playerOptions
                    });
                    window.open(routeData.href, '_blank');
                }

            })
        },
        //加载纪法课堂提交的所有的视频
        listLoading() {
            this.axios({
                method: 'post',
                url: '/backend/qlwz/queryInfoList',
                data: this.listQuery,
            }).then(response => {
                if (response.data.data) {
                    this.listData = response.data.data.rows;
                    this.total = response.data.data.total;
                }

            }).catch(() => {
                this.$message('查询失败！')
            })
        },
        //查询数据
        processSearch() {
            this.listQuery.pageIndex = 1;
            this.listLoading();
        },
        // 变更每页条数时自动加载数据
        handleSizeChange(val) {
            this.listQuery.pageSize = val;
            this.listLoading();
        },

        // 点击某一页时自动加载数据
        handleCurrentChange(val) {
            this.listQuery.pageIndex = val;
            this.listLoading();
        },

    }
}
</script>
<style scoped>
.showClass {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.image-container {
    display: flex;
    justify-content: center;
    width: 1000px;
    /* 与视频播放器相同的宽度 */
    margin-top: 10px;
    margin-bottom: 10px;
}

.image-item {
    width: calc(33.333% - 10px);
    margin-top: 5px;
    margin-right: 50px;
    transition: transform 0.3s ease;
    cursor: pointer;
    /* 鼠标悬停时显示小手 */
}

.image-item:last-child {
    margin-right: 0;
}

.el-row {
    width: 100%;
    /* 占满整个容器宽度 */
    margin-bottom: 15px;
    /* 与视频列表的间距 */
}

/* 调整输入框和按钮的样式 */
.el-input {
    width: 200px;
    /* 输入框宽度固定 */
    margin-right: 8px;
    /* 输入框和按钮之间的间距 */
    margin-left: 440px;
}

.el-button {
    margin-left: 8px;
    /* 按钮的左边距 */
}

.qlwz-list-paginationArea {
    width: 1200px;
    margin: center;
}

.video-list {
    width: 1200px;
    /* 与头部和尾部相同的宽度 */
    margin: auto;
    /* 水平居中 */
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    border-bottom: 1px solid #ebeef5;
    /* 分隔线 */
}

.video-name {
    flex-grow: 1;
    cursor: pointer;
}

.publish-time {
    flex-shrink: 0;
    margin-left: auto;
    /* 将时间推到最右边 */
    white-space: nowrap;
    /* 防止时间换行 */
}
</style>