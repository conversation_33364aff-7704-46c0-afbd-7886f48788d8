<template>

    <div class="left-navs">
      <div class="left_navs_fold">
        <div class="left_navs_fold_top">
          <div class="left_navs_fold_top_img">
            <img :src="SidebarFlag?'../../../static/img/themes/black/logo_hide.png':'../../../static/img/themes/black/logo.png'" :style="SidebarFlag?'height:30px':''">
          </div>
          <h1 class="left_navs_fold_top_title" :style="SidebarFlag?'opacity:0':'opacity:1'">{{runtimeCfg.app_project_name}}</h1>
          <!-- <div class="left_navs_fold_top_content" :style="SidebarFlag?'opacity:0':'opacity:1'">v1.2</div> -->
        </div>
      </div>

      <el-menu :class="SidebarFlag?'el-menu-scoped el-menu-scoped_hide':'el-menu-scoped'" :default-openeds="opendModule" @open="handleOpen" @close="handleClose" @select='handleSelect' :collapse="SidebarFlag" :unique-opened="true">
        <template v-for="(module,ind) in _modules">
            <NavMenu v-if="module.children && module.children.length" :module="module" :key="ind"></NavMenu>
            <el-menu-item v-else :key="module.resId" :index="module.resId" class="el-menu-item-scoped">
                <i v-if="module.iconClass" :class="module.iconClass" :style="module.iconColor!=''?{'color':module.iconColor}:''" ></i>
                <span class="el-submenu-item-title-scoped" v-html="module.resName" ></span>
            </el-menu-item>
        </template>
      </el-menu>
    </div>
</template>


<script scoped>
import NavMenu from "./NavMenu";
import {getResPvalueByResId,searchModulesTree} from "../../lib/comFun"
import buildConfig from "@static/js/runtime/config.js";
import {mixin,runtimeCfg,store} from "../../assets/core/index";

export default {
  components: {
    NavMenu
  },
  data() {
    return {
      isCollapse: false,
      index:1,
      searchKeys:"",
      buildConfig,
      imgPath:'../../../static/img/themes/black/logo.png',
      runtimeCfg
    };
  },
  props: { 
    modules: { 
      default: [] 
    }, 
    clickMode:{
      
    },
    SidebarFlag:Boolean,
  },
  methods: {
    handleOpen(resId, indexPath) {
      
      console.log("打开"+resId, indexPath);
    },
    handleClose(resId, indexPath) {
      
      console.log("关闭"+resId, indexPath);
    },

    handleSelect(resId, indexPath) {
      let res=getResPvalueByResId(resId)
      if(res&&res.resPvalue!="") 
      {
        this.$emit("beforemoduleclick",res.resPvalue); 
        this.$router.push({path:res.resPvalue});
        this.$emit("moduleclicked",res.resPvalue);
        console.log("点击"+res.resPvalue, indexPath);   
      }
      
    },
    tree_node_click(module,node,node1) {
      if(module.resId&&module.resId!=""&&module.resPvalue!="") 
      {
        //此处调用父子组件事件触发，区别于LeftTopNavs.vue中全局事件总线
        this.$emit("beforemoduleclick",module.resPvalue); 
        this.$router.push({path:module.resPvalue});
        this.$emit("moduleclicked",module.resPvalue);
        console.log("点击"+module.resPvalue)
      }
    },

  },
  computed:{
    _modules(){
      if(this.searchKeys!=""&&this.modules&&this.modules.length>0){
        return searchModulesTree(this.modules,"resName",this.searchKeys);
      }
      else{
        return this.modules;
      }
    },
    //当前需要展开的菜单,返回控件key绑定的属性值的数组
    opendModule(){
      let opend=[];
      this._modules.forEach(module => {
        opend.push(module.resId);
      });
      if(this.searchKeys!="")
      {
        return opend;
      }
      else
      {
        return [];
      }
        
    }    
  },
    
  mounted() {
    
  },
};
</script>
<style scoped>
/* 
.el-submenu__title:hover>.el-submenu-item-title-scoped{
  color: #0E3D9D !important;
}




*/
/* 图标颜色 */
/*

.left-navs>>> .el-menu-item.el-menu-item-scoped:hover>i{
    color:#0E3D9D !important;
}

.left-navs>>> .el-menu-scoped .el-menu-item-scoped:hover .el-submenu-item-title-scoped{ 
  color: #0E3D9D !important;
}


.left-navs>>>.el-menu-scoped .el-menu-item-scoped:hover{
  background-color: #e3effb !important;
}
 */



/* 重写框架左侧样式 */
*>>> .el-menu{
  /* background-color: ''; */
  background-color: transparent;
  border: 0px;
}
.left-navs>>> .el-menu .is-opened{
    background-color: #0E3D9D;
    border: 0px;
}

.el-menu-scoped>>> .el-submenu-item-title-scoped{
    color: #fff;
    font-family: '黑体';
}

.left-navs>>>.el-menu-scoped i{
  color: #fff;
}



.el-menu-scoped_hide>>> .el-submenu__title{
  margin-left: 0px !important;
}
/* 
.left-navs>>> .el-menu--inline .el-menu-item{
  margin-left: 20px;
} */



/* 一级选中 */
/* .left-navs>>> .el-menu .el-menu-item.el-menu-item-scoped.is-active{
  background-color: #073288;
  border-right: 5px solid #0a43b4;
} */
.left-navs>>> .el-menu-scoped .is-active>i{
  color:#ffffff !important;
}


.left-navs>>> .el-menu-scoped .is-active>span{
  color:#ffffff !important;
}
/* 二级以下选中效果 */
.left-navs>>> .el-menu-scoped .el-menu-item.el-menu-item-scoped.is-active{
  /* background-color: rgb(255 255 255 / 22%); */
  background-color: #575656;
  border-right: 5px solid #6598FF;

}


.left-navs >>>.el-menu-scoped .el-menu-item-scoped .el-menu--inline .is-active>i{
    color:#FFFFFF !important;
}
/* 选中图标 */
.left-navs>>>.el-menu-scoped .el-menu-item-scoped .el-menu--inline .is-active>i{
  color: #FFFFFF !important;
}


.left-navs >>>.el-menu-scoped .el-menu-item-scoped .el-menu--inline .is-active>span{
  color:#FFFFFF !important;
  font-weight: 600;
  font-family: system-ui;
}
.left-navs>>> .el-menu-scoped .el-menu-item-scoped .el-menu--inlined.is-active>.el-submenu-item-title-scoped{
    color:#0E3D9D !important;
}

/* 二级菜单样式重写 */
.left-navs>>>.el-menu-item-scoped .el-menu .el-submenu{
 /*  background-color: rgb(0 0 0 / 0%); */
  background-color: transparent;
  background-size: 100% 0%;
  /* padding-left: 20px; */
}

/* 悬浮效果 */


/* 二级菜单悬停样式 */
/* .left-navs>>>.el-menu-scoped .el-menu-item-scoped .el-menu--inline .el-menu-item-scoped:hover{
  background-color: #3A61B0;
  background-size: 100% 100%;
  transition: background-color 1s ease;
  cursor: url(../../../static/css/themes/zhjy/mchoose.svg),auto;
} */

.left-navs>>>.el-menu-scoped .el-menu-item-scoped .el-menu--inline .el-menu-item:hover{
  background-color: #3A61B0;
  background-size: 100% 100%;
  transition: background-color 1s ease;
  cursor: url(../../../static/css/themes/zhjy/mchoose.svg),auto;
}


/* 二级目录悬停 */
.left-navs>>> .el-menu-scoped .el-menu-item-scoped .el-menu--inline.el-menu-item-scoped:hover>i{
    color:#ffffff;
    transition: all 0.3s  ease-in-out;
    cursor: url(../../../static/css/themes/zhjy/mchoose.svg),auto;
}

.left-navs>>> .el-menu-scoped .el-menu-item-scoped .el-menu--inline.el-menu-item-scoped:hover>span{
    color:#ffffff;
    transition: all 0.3s  ease-in-out;
}
/* 

选中子项悬浮效果
.left-navs>>> .el-menu-scoped .el-menu-item.el-menu-item-scoped.is-active:hover>i{
  color:#ffffff !important;
  background-color: #ffffff;
  transition: all 0.3s  ease-in-out;
}

.left-navs>>> .el-menu-scoped .el-menu-item.el-menu-item-scoped.is-active:hover>.el-submenu-item-title-scoped{
  color:#ffffff !important;
  transition: all 0.3s  ease-in-out;
}

.left-navs>>> .el-menu-scoped .el-menu-item.el-menu-item-scoped.is-active:hover{
  background-color: rgba(0, 23, 155, 0.8);
} */



/* 选中子项悬浮效果 */
.left-navs>>> .el-menu-scoped .el-menu-item.el-menu-item-scoped.is-active:hover>i{
  color:#FFFFFF !important;
  transition: all 0.3s  ease-in-out;
}

.left-navs>>> .el-menu-scoped .el-menu-item.el-menu-item-scoped.is-active:hover>.el-submenu-item-title-scoped{
  color:#FFFFFF !important;
  transition: all 0.3s  ease-in-out;
}

.left-navs>>> .el-menu-scoped .el-menu-item.el-menu-item-scoped.is-active:hover{
 /*  background-color: #3A61B0; */
}


/* 一级目录 */

.left-navs>>> .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover,
.left-navs>>> .el-menu-scoped .el-menu-item:hover{
  /* background-color: rgb(0 0 0 / 0%); */
  background-color: transparent;
  cursor: url(../../../static/css/themes/zhjy/mchoose.svg),auto;
}

.left-navs>>> .el-submenu__title:hover>i{
  color:#ffffff !important;
  transition: all 0.3s  ease-in-out;
}
.left-navs>>> .el-submenu__title:hover>.el-submenu-item-title-scoped{
  color:#ffffff !important;
  transition: all 0.3s  ease-in-out;
}

.left-navs>>> .el-submenu__title:focus, .el-submenu__title:hover{
  background-color: transparent;
}
</style>