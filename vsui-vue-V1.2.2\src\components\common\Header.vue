<template>
  <div class="header-box" :class="{'hidestate':hideHeader}">
    <div class="header">
      <div class="lf left-top-menu"
         @mouseover="left_navs_collapse=false"
         @mouseout="left_navs_collapse=true" 
         @click.stop="left_navs_collapse=!left_navs_collapse" 
         @touchend="function(e){ left_navs_collapse=!left_navs_collapse;e.preventDefault();}">
        <div class="left-top-menu-icon"><i class="fa fa-bars"></i></div>
      </div>
      <router-link class="lf logo" to="/Dashboard" tag='div' />
      <div class="lf sysName">{{runtimeCfg.app_project_name}}</div>
      <div class="header-right rf">
          <!-- 全局查询 -->
          <div class="lf searchinfo">
              <i class="fa fa-search"></i>
              <el-input class="searchinput"  v-model="searchKeys" placeholder="请输入检索内容">
                <el-button slot="append" class="searchbtn" icon="fa fa-search"></el-button>
              </el-input>

          </div>
          <!-- 时间显示 -->
          <div class="lf timeinfo">
              {{(new Date()).format("yyyy年MM月DD日")}}
              &nbsp;
              星期{{['一','二','三','四','五','六','日'][(new Date()).getDay()-1]}}
          </div>
          <!-- 用户头像 -->
          <div class="lf userinfo">
              <i class="fa fa-user-o"></i>
              欢迎您 {{auth.instance.getUserName()}}
          </div>
          <!-- 隐藏头部 -->
          <div class="lf hideheader" :title="hideHeader?'显示头部':'隐藏头部'" @click="hideHeader=!hideHeader">
            <i class="fa fa-arrows-v"></i>
          </div>
          <!-- 全屏显示 -->
          <div class="lf fullscreen" :title="fullScreen?'还原':'最大化'" @click="handleFullScreen">
            <i class="fa " :class="{'fa-window-restore':fullScreen,'fa-window-maximize':!fullScreen}"></i>
          </div>
          <!-- 主题切换 -->
          <div class="lf themeswitch" @mouseover="theme_sel_collapse=false" @mouseout="theme_sel_collapse=true" @click="theme_sel_collapse=!theme_sel_collapse">
            <i class="fa fa-tachometer"></i>
          </div>
          <!-- 消息中心 -->
          <router-link to="/tabs" tag="div" class="lf messagehint">
            <el-badge :title="message?`有${message}条未读消息`:``" :value="message?message:``" class="">
              <i class="fa fa-bell-o"></i>
            </el-badge>
          </router-link>
          <!-- 系统设置 -->
          <div class="lf setting" title="系统设置">
            <i class="fa fa-cog"></i>
          </div>
          <!-- 用户退出 -->
          <div class="lf logout" @click="logOut" title="注销退出">
              <i class="fa fa-sign-out"></i>
          </div>

        
      </div>
      
    </div>
    <div class="showheader" title="显示头部" :class="hideHeader?'show':'hide'" @click="hideHeader=!hideHeader" >
      <i class="fa fa-caret-square-o-down"></i>
    </div>
    <div class="left-navs"  :class="{'left-navs_collapse':left_navs_collapse}"  @mouseover="left_navs_collapse=false" @mouseout="left_navs_collapse=true" >
          <left-top-navs :modules="modulesTree" @beforemoduleclick="beforemoduleclick" @moduleclicked="moduleclicked"/>
        </div>
    <div class="themes-sel" :class="{'themes-sel_collapse':theme_sel_collapse}"  @mouseover="theme_sel_collapse=false" @mouseout="theme_sel_collapse=true">
          <div v-for="theme in themeInfo.themes" :key="theme.path" class="lf theme_item" :class="{'current_theme':themeName==theme.name}" @click="handleSelTheme(theme)">
            <div class="imgBox">
              <img border="0" :src="themeInfo.themeImgUrl.format(theme.path,theme.img)"/>
            </div>
            <div class="txtBox">{{theme.name}}</div>
          </div>
    </div>
    <div class="top-navs">
      <router-link to="/Dashboard" tag='div' class="lf el-menu-item-home">
        <i class="fa fa-home"></i>
      </router-link>
      
        <el-menu :default-active="activeIndex" class="el-menu-scoped" mode="horizontal" @select="handleSelect">
          <el-menu-item v-for="module in modulesTree" :key="module.resId" :index="module.resId" class="el-menu-item-scoped">
            <span class="el-menu-item-span-scoped">{{module.resName}}</span>
            <!--<router-link :to="module.resPvalue" :key="module.resId" tag='span' class="el-menu-item-span-scoped">{{module.resName}}</router-link>-->
          </el-menu-item>
        </el-menu>
      
    </div>
  </div>
</template>

<script>
import {getResPvalueByResId} from "../../lib/comFun"
import LeftTopNavs from "./LeftTopNavs";
import {changeTheme} from "../../lib/Theme.js";
import {Class,Screen} from "@vsui/lib-jsext";
import themeInfo from "../../assets/configs/themeInfo"
import { mapState } from 'vuex';
import {mixin,runtimeCfg,store} from "../../assets/core/index";



export default {
  components: {
    LeftTopNavs
  },
  mixins:[mixin],
  data() {
    return {
      activeIndex: "0",
      fullScreen:false,
      hideHeader:false,
      themeInfo,
      runtimeCfg,
      left_navs_collapse:true,//左上角菜单收起
      theme_sel_collapse:true,//主题列表收起
      message:1,
      searchKeys:"",
    };
  },

  methods: {
    // 用户名下拉菜单选择事件
    logOut:function()
    {
      const _this = this;
      this.auth.instance.logout().then(()=>{})
      .catch(error=>{
        _this.$message({
              message: `退出过程出错，失败原因是：${error.message}`,
              dangerouslyUseHTMLString: true,
              type: "error"
            });
      });     
    },
    handleSelect(resId, indexPath) {
      let res=getResPvalueByResId(resId)
      if(res&&res.resPvalue!="") 
      {
        this.$emit("beforemoduleclick",res.resPvalue); 
        this.$router.push({path:res.resPvalue});
        this.$emit("moduleclicked",res.resPvalue);
        console.log("点击"+res.resPvalue, indexPath);   
      }
      
    },
    handleSelTheme(theme){
      const _this=this;
      this.theme_sel_collapse=true;
      
        changeTheme(theme,
        (theme,newthemeDom)=>
        {
              store.commit('APP/setTheme', theme.name);
        },
        err=>{
            _this.$message({
            message: `${err.message}`,
            dangerouslyUseHTMLString: true,
            type: "error"
          });
        }
      )
    },
    // 全屏事件
    handleFullScreen() {
      this.fullScreen=Screen.screenSwitch();
    },

    /**
     * 侧边栏点击前处理事件，
     * 
     */
    beforemoduleclick(routerPath){

    },

    /**
     * 侧边栏点击后处理事件，
     * 
     */
    moduleclicked(routerPath)
    {
      console.log("点击链接"+routerPath);
    },
    
  },
  computed: {
    ...mapState({themeName:state=>state.APP.theme}),
    modulesTree : function(){return this.auth.instance.getModulesTree();},
  },
  created(){
  },
  beforeMount() {
      
  },
  mounted() {
    let _this=this;
    changeTheme(this.themeInfo.themes.find((item, index, arr)=>item.name==this.themeName))
    this.$EventBus.$on("documentclick", (e) => {
       if(!_this.left_navs_collapse) {
          _this.left_navs_collapse=true;
        }
       if(!_this.theme_sel_collapse) {
        _this.theme_sel_collapse=true;
       }
    });
  }
};
</script>
