@import url("lefttopnavs.css");
/**************************头部样式开始*********************************/
.app .wrapper .header-box{
    transition: all 0.2s ease-in-out;
    background-color:#ffffff;
    
}
.app .wrapper .header-box .header {
    color: #333333;
    border-bottom:1px solid #dddddd;
}
.app .wrapper .header-box.hidestate {

    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box.hidestate .showheader
{
    opacity: 1;
    color:#333333;
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .header .left-top-menu {
    border-right:1px solid #dddddd;
    background-color: #2384B7;
    color:#ffffff;
    font-size: 22px;
    transition: all 0.1s ease-in-out;
}
.app .wrapper .header-box .header .left-top-menu:hover {
    
    font-size: 26px;
}
.app .wrapper .header-box .header .left-top-menu .left-top-menu-icon
{
    font-size: inherit;
}

.app .wrapper .header-box .header .logo {
    background: url(../../../img/themes/whitegray1/logo.png);
    background-position: center;
    background-repeat: no-repeat;
    background-origin: content-box;
    background-size: 90px 40px;
    background-clip: content-box;
}
.app .wrapper .header-box .header .logo img{

}
.app .wrapper .header-box .header .sysName {

}

.app .wrapper .header-box .header .header-right >div {
    /*border-left: 1px  solid #dddddd;*/
    font-size: 12px;
    transition: all 0.1s ease-in-out;
}
.app .wrapper .header-box .header .header-right >div:first-child {
    /*border-left: 1px  solid #dddddd;*/
}
.app .wrapper .header-box .header .header-right >div:last-child {
    
}
.app .wrapper .header-box .header .header-right >div:hover {
    color:#2384B7;
    
}
.app .wrapper .header-box .header .header-right .searchinfo
{

}
.app .wrapper .header-box .header .header-right .searchinfo .searchinput
{
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .header .header-right .searchinfo:hover .searchinput
{
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .header .header-right .searchinfo:hover .searchinput .el-input-group__append{
    background-color:#eeeeee
}
.app .wrapper .header-box .header .header-right .searchinfo .searchinput input.el-input__inner
{
    background-color: #eeeeee;
    color: #fff;
}
.app .wrapper .header-box .header .header-right .searchinfo .searchinput button.searchbtn
{
    color:#333333;
}
.app .wrapper .header-box .header .header-right .timeinfo {
    
}

.app .wrapper .header-box .header .header-right .userinfo {
    
}

.app .wrapper .header-box .header .header-right .hideheader:hover {
    font-size: 14px;
}

.app .wrapper .header-box .header .header-right .hidingheader:hover{
    font-size: 14px;
}

.app .wrapper .header-box .header .header-right .fullscreen:hover {
    font-size: 14px;
}

.app .wrapper .header-box .header .header-right .themeswitch:hover {
    font-size: 14px;
}

.app .wrapper .header-box .header .header-right .messagehint:hover {
    font-size: 14px;
}

.app .wrapper .header-box .header .header-right .setting:hover{
    font-size: 14px;
}

.app .wrapper .header-box .header .header-right .logout:hover {
    font-size: 14px;
}



/**********************上部通栏导航***************************/
.app .wrapper .header-box	.top-navs {
    background-color:transparent;
    /*border-bottom: 1px solid transparent;*/
    color:#333333;
    box-shadow:rgba(0, 0, 0, 0.08) 0px -2px 2px 0px inset;
}

.app .wrapper .header-box	.top-navs .el-menu-item-home
{
    border-right: 1px solid #dddddd;
    transition: all 0.1s ease-in-out;
    font-size: 16px;
    color:#2384B7;
}
.app .wrapper .header-box .top-navs .el-menu-item-home:hover
{
    font-size: 20px;
}

.app .wrapper .header-box .top-navs .el-menu-scoped
{
    background-color: transparent !important;
}

.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped
{
    background-color: transparent;
    border-top: transparent solid 2px ;
    transition: all 0.2s ease-in-out;
    color:#333333;
}



.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped:hover
{
    color: #111111 !important;
    font-weight: bolder;
}
.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active]
{
    color:#333333;
    font-weight:700;
    background-color:#eeeeee;
}
      
.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped .el-menu-item-span-scoped
{
        
}
    
/*************************左上角可展开导航栏**********************************/
.app .wrapper .header-box .left-navs
{
    border-right:1px solid #dddddd;
    transition:all 0.2s ease-in-out;
    background: #ffffff;
    -webkit-transition: all 0.2s ease-in-out;
    color:#333333;
}
.app .wrapper .header-box .left-navs_collapse
{
}

.app .wrapper .header-box .themes-sel
{
    border-left: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;
    background: #ffffff;
    color:#333333;
    transition:all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .themes-sel .theme_item
{
    
    border-right:1px solid #dddddd;
    border-bottom:1px solid #dddddd;
}
.app .wrapper .header-box .themes-sel .theme_item[class~=current_theme]
{
    background-color: #dddddd;
}

.app .wrapper .header-box .themes-sel .theme_item:hover
{
    background-color: #eeeeee;
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .themes-sel .theme_item:hover img
{
    opacity: 1;
}
/**************************头部样式完毕*********************************/

/********************加载条样式开始*****************************/
#nprogress {
    pointer-events: none;
  }
  
  #nprogress .bar {
    background: #2384B7;
  
    position: fixed;
    z-index: 1031;
    top: 0;
    left: 0;
  
    width: 100%;
    height: 2px;
  }
  
  /* Fancy blur effect */
  #nprogress .peg {
    display: block;
    position: absolute;
    right: 0px;
    width: 100px;
    height: 100%;
    box-shadow: 0 0 10px #2384B7, 0 0 5px #2384B7;
    opacity: 1.0;
  
    -webkit-transform: rotate(3deg) translate(0px, -4px);
        -ms-transform: rotate(3deg) translate(0px, -4px);
            transform: rotate(3deg) translate(0px, -4px);
  }
  
  /* Remove these to get rid of the spinner */
  #nprogress .spinner {
    display: block;
    position: fixed;
    z-index: 1031;
    top: 15px;
    right: 15px;
  }
  
  #nprogress .spinner-icon {
    width: 18px;
    height: 18px;
    box-sizing: border-box;
  
    border: solid 2px transparent;
    border-top-color: #2384B7;
    border-left-color: #2384B7;
    border-radius: 50%;
  
    -webkit-animation: nprogress-spinner 400ms linear infinite;
            animation: nprogress-spinner 400ms linear infinite;
  }
  
  .nprogress-custom-parent {
    overflow: hidden;
    position: relative;
  }
  
  .nprogress-custom-parent #nprogress .spinner,
  .nprogress-custom-parent #nprogress .bar {
    position: absolute;
  }
  
  @-webkit-keyframes nprogress-spinner {
    0%   { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
  }
  @keyframes nprogress-spinner {
    0%   { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
/****************加载条样式结束*********************************/