<template>
    <div class="zhyy-list-container">
        <div class="zhyy-list-main">
            <div class="zhyy-list-searchArea">
                <el-row>
                    <el-col :span="18">
                        <label >表单名称：</label>
                        <el-input @keyup.enter.native="processSearch" style="width: 200px;"
                            class="filter-item" placeholder="表单名称" v-model="listQuery.name" >
                        </el-input>
                        <label style="margin-left:8px;">表单ID：</label>
                        <el-input @keyup.enter.native="processSearch" style="width: 200px;"
                            class="filter-item" placeholder="表单ID" v-model="listQuery.id" >
                        </el-input>
                        <label >下拉选择：</label>
                        <el-select v-model="listQuery.selectValue"  style="width: 170px;" clearable>
                            <el-option v-for="(item, index) in selectList" :key="index" :label="item.text"
                                    :value="item.id"></el-option>
                        </el-select>

                        <el-button type="primary" icon="el-icon-search" style="margin-left:8px;"
                            @click="processSearch">查询</el-button>
                        
                        <!-- <el-button type="default" icon="el-icon-plus" 
                            @click="popSelect">选择</el-button> -->


                    </el-col>
                    <el-col :span="6" style="text-align:right">
                        <el-button type="success" icon="el-icon-plus" @click="addData">新建</el-button>
                        <el-button type="success" icon="el-icon-document-copy" @click="copyData">复制</el-button>
                        <el-button type="danger" icon="el-icon-delete" @click="deleteData">删除</el-button>

                    </el-col>
                </el-row>
            </div>

            
            <el-row class="zhyy-list-tableArea" >
                <el-table 
                    :stripe="true"
                    v-loading="listLoading" 
                    highlight-current-row
                    ref="listTable"
                    :data="tableData" 
                    :row-key="getRowKey"
                    :header-cell-style="{ background: '#F4F7FA'}"

                    @row-click="onSelect"

                    >
                    
                    <el-table-column type="selection" min-width="5%" align="center" :reserve-selection="true"></el-table-column>
                    <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px">
                    </el-table-column>
                    <el-table-column min-width="20%" align="left" header-align="center" label="表单名称">

                        <template slot-scope="scope">
                            <span>{{scope.row.NAME}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="10%" align="center" header-align="center"  label="表单类型">
                        <template slot-scope="scope">
                            <span>{{scope.row.LBMC}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="10%" align="left" header-align="center" label="表单ID">
                        <template slot-scope="scope">
                            <span>{{scope.row.FORMID}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="10%" align="left" header-align="center" label="返回值代码">
                        <template slot-scope="scope">
                            <span>{{scope.row.CODE}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="20%" align="center" header-align="center" label="录入时间">
                        <template slot-scope="scope">
                            <span>{{scope.row.CRTIME}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="20%" label="操作" align="center" header-align="center" >
                        <template slot-scope="scope">
                            <el-button size="mini" type="text" icon="el-icon-edit"
                                @click="popEditData(scope.$index, scope.row)">弹出</el-button>
                            <el-button size="mini" type="text" icon="el-icon-edit"
                                @click="toEditData(scope.$index, scope.row)">跳转</el-button>
                            <el-button size="mini" type="text" icon="el-icon-delete"
                                @click="deleteData(scope.$index, scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="zhyy-list-paginationArea">
                    <el-pagination background 
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange" 
                        :page-sizes="[10, 20, 30, 50]" 
                        :page-size="listQuery.pageSize"
                        :current-page="listQuery.pageIndex"
                        layout="total, sizes, prev, pager, next, jumper" 
                        :total="total"
                        ></el-pagination>
                </div>
            </el-row>

        </div>

        <!-- 以下是简单弹出页 -->
        <el-dialog  v-dialogDrag 
            v-if="popFormVisable"
            title="简单弹出页" 
            width="880px"
            :visible.sync="popFormVisable" 
            :before-close="handleClosePopFormDialog" 
            :close-on-click-modal="false" >

            <popForm 
            :id="actId" 
            :newId="newId" 
            :operation="operation" 
            @confirmSelect="handleClosePopFormDialog" 
            @clearSelect="handleClosePopFormDialog" 
            />

        </el-dialog>

        <!-- 以下是动态标题的弹出页 -->
        <el-dialog  v-dialogDrag 
            v-if="getSubmitViewVisable"
            width="60%"
            :title="editFormTitle"
            :visible.sync="getSubmitViewVisable" 
            :before-close="handleCloseViewDialog" 
            :close-on-click-modal="false" >

            <editForm 
                :id="actId" 
                :newId="newId" 
                :operation="operation" 
                @handleSubmitSuccess="handleSubmitSuccess"  
                @handleCloseViewDialog="handleCloseViewDialog"
             />

        </el-dialog>
        


    </div>
</template>

<script>
import editForm from "./zhyy-edit-dialog";
import popForm from "./zhyy-pop";
import { v4 as getUUID } from 'uuid'
import VSAuth from "@vsui/lib-vueauth4vseaf";
export default {
    name: "index",
    
    components: {
        editForm,
        popForm,
    },



    data() {
        return {
            user:VSAuth.getAuthInfo(),
            title: "表单工具列表",

            total: null,
            listLoading: true,
            tableData: undefined,
            pkColumn: 'ID',
            listQuery: {
                pageIndex: 1,
                pageSize: 10,
                name: "",
                id: "",
                selectValue:""
            },

            editFormTitle:'',

            actId: '',
            operation: '',
            newId: '',

            getSubmitViewVisable: false,
            popFormVisable: false,
            selectList:[{id:'1',text:'下拉1'},{id:'1',text:'下拉2'}]

        };
    },
    mounted() {
       /**
        *  公共方法使用示例
        *  console.log(this.utils.getTimestamp());
        *  console.log(this.files.XMYX_XMLX_XGFJ);
        * console.log(this.utils.getUid());
        */
       
       
        this.loadListData();
    },
    methods: {
        /**
		 * @description: 加载列表数据
		 * @param {*}
		 * @return {*}
		 */
        loadListData() {
            this.listLoading = false;
            this.tableData = [
                {
                    ID:'1',
                    NAME:'这是测试数据第一条',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'2',
                    NAME:'测试测试测试测试测试测试测试测试测试测试测试',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'3',
                    NAME:'第一条第一条第一条第一条第一条第一条第一条第一条',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'4',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },

            ];

            this.total = 1;
        },

        /**
		 * @description: 查询数据，需要把页数重置为1
		 * @param {*}
		 * @return {*}
		 */
        processSearch() {
            this.listQuery.pageIndex = 1;
            this.loadListData();
        },

        /**
		 * @description: 变更每页条数时自动加载数据
		 * @param {*}
		 * @return {*}
		 */
        handleSizeChange(val) {
            this.listQuery.pageSize = val;
            this.loadListData();
        },
        
        /**
		 * @description: 点击某一页时自动加载数据
		 * @param {*}
		 * @return {*}
		 */
        handleCurrentChange(val) {
            this.listQuery.pageIndex = val;
            this.loadListData();
        },


        /**
		 * @description: 弹出页面提交后回调函数
		 * @param {*}
		 * @return {*}
		 */
        handleSubmitSuccess() {
            this.getSubmitViewVisable = false;
            this.loadListData();
        },

        /**
		 * @description: 弹出页面点击关闭按钮回调函数
		 * @param {*}
		 * @return {*}
		 */
        handleCloseViewDialog() {
            this.getSubmitViewVisable = false;
        },
        

        /**
		 * @description: 打开简单的弹出选择页
		 * @param {*}
		 * @return {*}
		 */
        popSelect() {
            this.popFormVisable = true;
        },
        /**
		 * @description: 关闭简单的弹出选择页
		 * @param {*}
		 * @return {*}
		 */
        handleClosePopFormDialog(done) {
            this.popFormVisable = false;
        },


        /**
		 * @description: 打开新增页面 - 弹出方式
		 * @param {*}
		 * @return {*}
		 */
        addData() {
            this.actId = getUUID().replace(/-/g, '').toUpperCase();
            this.editFormTitle = '新增表单信息';
            this.operation = 'add';
            this.getSubmitViewVisable = true;
        },

        /**
		 * @description: 打开编辑页面 - 弹出方式
		 * @param {*}
		 * @return {*}
		 */
        popEditData(index, row) {
            this.actId = row.ID;
            this.operation = 'edit';
            this.editFormTitle = '修改表单信息';
            this.getSubmitViewVisable = true;

        },
        /**
		 * @description: 打开编辑页面 - 跳转页面方式
		 * @param {*}
		 * @return {*}
		 */
        toEditData(index, row) {
            var pageName = '/demo/zhyyEditForm';
           
           /*  this.$router.push({
                name: pageName,
                path: '/' + pageName,
                params: {
                    id: row.ID
                }
            }); */

            this.$EventBus.$emit("moduleclicked",pageName); 


        },

        /**
		 * @description: 复制数据
		 * @param {*}
		 * @return {*}
		 */
        copyData() {
            let currentSelect = this.$refs.listTable.selection;
            console.log(currentSelect);
            if(currentSelect.length == 0){
                this.$notify({title: "提示", message: "请先选择要复制的行！", type: "info", duration: 2000, offset: 90});
                return;
            }
            if(currentSelect.length > 1){
                this.$notify({title: "提示", message: "请选择一行数据！", type: "info", duration: 2000, offset: 90});
                return;
            }
            this.actId = currentSelect[0].ID;
            this.editFormTitle = '复制表单信息';
            this.operation = 'copy';
            this.newId = getUUID().replace(/-/g, '').toUpperCase();
            this.getSubmitViewVisable = true;
        },

        /**
		 * @description: 删除数据
		 * @param {*}
		 * @return {*}
		 */
        deleteData(index, row) {
            console.log(JSON.stringify(row));

            this.$confirm("此操作将永久删除当前数据，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
            .then(() => {
                RESTAPI.delDataFormConfigData({
                        id: row.ID
                    },
                    response => {
                        if (response && response.data && response.data.result == 20000) {
                            this.$notify({
                                title: "提示",
                                message: "删除成功！",
                                type: "success",
                                duration: 2000,
                                offset: 80
                            });
                            this.loadListData();
                        } else {
                            this.$notify({
                                title: "警告",
                                message: "删除表单信息失败！" + +response.data.msg,
                                type: "warning",
                                duration: 2000,
                                offset: 80
                            });
                        }
                    },
                    error => {
                        this.$notify({
                            title: "警告",
                            message: "删除表单信息失败！" + error,
                            type: "warning",
                            duration: 2000,
                            offset: 80
                        });
                    }
                );

            })
            .catch(err => {
            });
        },

        getRowKey(rowData){
            return rowData[this.pkColumn];
        }, 
        onSelect(val){
            //如果是单选
            var isSingleSelect = true;
            if(isSingleSelect){
                let currentSelect = this.$refs.listTable.selection;
                if(currentSelect.length > 0){
                    this.$refs.listTable.clearSelection();
                    if(currentSelect[0][this.pkColumn] != val[this.pkColumn]){
                        this.$refs.listTable.toggleRowSelection(val);
                    }
                }else{
                    this.$refs.listTable.toggleRowSelection(val);
                }
            }else{
                this.$refs.listTable.toggleRowSelection(val);

            }
            
        },
        
    },
    
};

</script>


<style scoped>

</style>