<template>
    <div class="dialog_form">
        <el-form ref="formData" :model="formData" :rules="rules" size="small" label-width="100px">

            <el-form-item label-width="80px" label="标题" prop="infoTitle">
                <el-input v-model="formData.infoTitle" placeholder="请输入标题" clearable :style="{ width: '100%' }"
                    :maxlength="100" show-word-limit :disabled="viewVisable">
                </el-input>
            </el-form-item>

            <el-form-item label-width="80px" label="附件" required="true">
                <vsfileupload v-if="showUpload && !viewVisable" ref="upload" :busId="fileParams.busId" :ywlb="type+'file'">
                </vsfileupload>
                <vsfileupload class="uploadFile" v-if="viewVisable && showUpload" ref="upload" :busId="fileParams.busId" :ywlb="type+'file'" :editable="false">
                </vsfileupload>

            </el-form-item>

            <el-form-item label-width="80px" label="备注" prop="infoNotes">
                <el-input v-model="formData.infoNotes" type="textarea" placeholder="请输入备注"
                    :autosize="{ minRows: 8, maxRows: 16 }" :style="{ width: '100%' }" :maxlength="4000"
                    show-word-limit :disabled="viewVisable">
                </el-input>
            </el-form-item>

            <el-form-item size="large" align="center" class="button_style">
                <el-button v-if="!viewVisable" type="primary" @click="saveData(0)">保存</el-button>
                <el-button v-if="!viewVisable" type="success" @click="submitForm(1)">提交</el-button>
                <el-button @click="closeForm">关闭</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import vsfileupload from '../../common/vsfileupload.vue';
import { v4 as getUUID } from 'uuid';
import VSAuth from "@vsui/lib-vueauth4vseaf";
export default {
    components: {
        vsfileupload,
    },
    props: {
        type: {
            type: String,
            default: ""
        },
        rowData: Object,//列表传入的行数据
        operation: String,//用来区分是新增还是编辑

    },
    data() {
        return {
            fileParams: {
                busId: '',//业务ID
                ywlb: '',//附件业务类型，区分同一数据多个文件类型
            },
            formData: {
                infoId: '',//主键
                infoTitle: '',//标题
                infoNotes: '',//备注
                infoType: '',//类型
                processStatus: '',//审核状态
                infoStatus: '',//发布状态
                publishTime: '',//发布时间
                operatorId: '',//发布人id
                operatorName: '',//发布人名称
                operatororgId: '',//发布人单位id
                operatororgName: '',//发布人单位名称
            },
            rules: {
                infoTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],

            },
            showUpload: false,
            viewVisable: false,
        }
    },
    mounted() {
        this.formData.infoType=this.type;
        if (this.operation == 'edit') {
            this.formData.infoId = this.rowData.infoId;

        }
        
        if(this.operation=='view'){
            this.formData.infoId = this.rowData.infoId;
            this.viewVisable=true;
        }
        this.getInfoData();
    },
    methods: {
        //保存信息
        saveData(zt) {
            var isValid = false;
            this.$refs.formData.validate((valid) => {
                isValid = valid;
            });
            if (!isValid) {
                this.$message({ message: '页面有必填项未填写', type: 'error' });
                return;
            }
            let fileList = this.$refs.upload.fileList;
            if (!fileList || fileList.length == 0) {
                this.$notify({ title: '警告', message: '请上传附件', type: 'warning' });
                return;
            }


            if (zt == 0) {//点击保存按钮
                this.formData.processStatus = '0';//草稿状态
                this.formData.infoStatus = '0';//未发布状态
            } else if (zt == 1) {
                this.formData.processStatus = '1';//党支部审核状态
                this.formData.infoStatus = '1';//发布状态
            }
            this.formData.operatorId = VSAuth.getAuthInfo().permission.userId;
            this.formData.operatorName = VSAuth.getAuthInfo().permission.userName;  
            this.formData.operatororgId = VSAuth.getAuthInfo().permission.orgnaId;
            this.formData.operatororgName = VSAuth.getAuthInfo().permission.orgnaName;
            this.axios({
                method: "post",
                url: "/backend/qlwz/saveInfoData",
                data: this.formData

            }).then(response => {
                if (response.data.data.status == 'success') {
                    this.$message.success("保存成功！");
                    this.closeForm();
                } else {
                    this.$message.error("保存失败！");
                }
            }).catch(error => {
                this.$message.error("保存失败！")
            })


        },
        //提交数据
        submitForm(zt) {
            this.$confirm("确定要提交吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.saveData(zt);

            }
            ).catch(() => { });

        },
        //获取某条数据:根据infoId查询数据
        getInfoData() {
            this.axios({
                method: "post",
                url: "/backend/qlwz/queryInfoDataById",
                data: {
                    infoId: this.formData.infoId,
                },
            }).then(resp => {
                if (resp.data.data && resp.data.data.infoId) {
                    this.formData = resp.data.data;
                    this.fileParams.busId = this.formData.infoId;
                } else {
                    this.formData.infoId = getUUID().replace(/-/g, '').toUpperCase();
                    this.fileParams.busId = this.formData.infoId;
                }
                this.showUpload = true;


            })

        },
        //关闭弹窗
        closeForm() {
            this.$emit('handleCloseViewDialog');
        },
    }


}
</script>
<style scoped>
.uploadFile{
    margin-top: -37px;
}
.button_style{
    margin-left: -100px;
}
</style>