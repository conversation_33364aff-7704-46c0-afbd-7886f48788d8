<template>
    <div class="dialog_form">
        <el-form ref="formData" :model="formData" :rules="rules" size="small" label-width="100px">

            <el-form-item label-width="80px" label="标题" prop="infoTitle">
                <el-input v-model="formData.infoTitle" placeholder="请输入标题" clearable :style="{ width: '100%' }"
                    :maxlength="100" show-word-limit>
                </el-input>
            </el-form-item>

            <el-form-item label-width="80px" label="附件" required="true">
                <vsfileupload v-if="showUpload" ref="upload" :busId="fileParams.busId"
                    :ywlb="formData.infoType + 'file'">
                </vsfileupload>

            </el-form-item>

            <el-form-item label-width="80px" label="备注" prop="infoNotes">
                <el-input v-model="formData.infoNotes" type="textarea" placeholder="请输入备注"
                    :autosize="{ minRows: 8, maxRows: 16 }" :style="{ width: '100%' }" :maxlength="4000"
                    show-word-limit>
                </el-input>
            </el-form-item>

            <el-form-item size="large" align="center" class="button_style">
                <el-button type="primary" @click="performSave">保存</el-button>
                <el-button type="success" @click="saveData">置回草稿</el-button>
                <el-button @click="closeForm">关闭</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import vsfileupload from '../../../common/vsfileupload.vue';
export default {
    components: {
        vsfileupload,
    },
    props: {
        rowData: Object,//列表传入的行数据

    },
    data() {
        return {
            fileParams: {
                busId: '',//业务ID
                ywlb: '',//附件业务类型，区分同一数据多个文件类型
            },
            formData: {
            },
            rules: {
                infoTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],

            },
            showUpload: false,
        }
    },
    mounted() {
        this.getInfoData();
    },
    methods: {
        //保存信息
        async saveData(zt) {
            var isValid = false;
            this.$refs.formData.validate((valid) => {
                isValid = valid;
            });
            if (!isValid) {
                this.$message({ message: '页面有必填项未填写', type: 'error' });
                return;
            }
            let fileList = this.$refs.upload.fileList;
            if (!fileList || fileList.length == 0) {
                this.$notify({ title: '警告', message: '请上传附件', type: 'warning' });
                return;
            }
            if (zt == 1) { // 保存
                this.performSave();
            } else { // 置回草稿
                this.confirmAndSaveAsDraft();
            }
        },
        // 确认并置回草稿
        async confirmAndSaveAsDraft() {
            const confirmResult = await this.$confirm("此操作将该条数据退回草稿，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            });
            if (confirmResult) {
                this.formData.processStatus = '0'; // 退回草稿状态
                this.formData.infoStatus = '0'; // 退回未发布状态
                this.formData.glyth = 1; // 管理员退回至草稿的标识
                this.performSave();
            }
        },
        // 执行保存操作
        async performSave() {
            try {
                const response = await this.axios({
                    method: "post",
                    url: "/backend/qlwz/saveInfoData",
                    data: this.formData
                });
                if (response.data.data.status == 'success') {
                    this.$message.success("操作成功！");
                    this.closeForm();
                } else {
                    this.$message.error("操作失败！");
                }
            } catch (error) {
                this.$message.error("操作失败！");
            }
        },
        //获取某条数据:根据infoId查询数据
        getInfoData() {
            this.axios({
                method: "post",
                url: "/backend/qlwz/queryInfoDataById",
                data: {
                    infoId: this.rowData.infoId,
                },
            }).then(resp => {
                if (resp.data.data && resp.data.data.infoId) {
                    this.formData = resp.data.data;
                }
                this.fileParams.busId = this.formData.infoId;
                this.showUpload = true;
            })

        },
        //关闭弹窗
        closeForm() {
            this.$emit('handleCloseViewDialog');
        },
    }


}
</script>
<style scoped>
.button_style {
    margin-left: -100px;
}
</style>