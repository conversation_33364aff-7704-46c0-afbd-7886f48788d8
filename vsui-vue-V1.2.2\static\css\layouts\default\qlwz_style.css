
/********************************** 以下是清廉网站页面优化定义的样式 *********************************************/
.background-video{
    left: 50%;
    min-height: 110%;
    min-width: 100%;
    position: fixed;
    top: 50%;
    transform: translate(-50%,-50%);
    width: 100%;
    background-size:100% 100%;
  }
  
  .qlwz-list-container{
      /* 普通列表页面整体背景 */
      /* background-color:#f2f2f2; */
      background-color:#f4f5f9;
      height: 100%;
    }
    .qlwz-list-main{
      /* 普通列表页主区域 */
      padding:8px 12px;
      background-color: #f4f5f9;
    }
    
    .qlwz-list-searchArea{
      /* 列表页面检索区域 */
      padding:13px;
      background-color:#ffffff;
      margin-bottom:8px;
      border:1px solid #EBEEF5;
      border-radius: 5px;
      box-shadow: 1px 1px 4px #d7d7d7 ;
    }
    .qlwz-list-tableArea{
      /* 列表页面数据区域 */
      background-color:#ffffff;
      box-shadow: 1px 1px 4px #b3b3b3;
    }
    .qlwz-list-searchAreaPop{
      /* 列表页面检索区域-用于弹出列表的 */
      margin-bottom:3px;
      background-color:#ffffff;
      padding: 10px;
      
    }
  
    *>>>.el-table--scrollable-x .el-table__body-wrapper {
        overflow-x: auto;
        overflow-y: auto;
    }
  
  
  
    .qlwz_search_div{
        display: inline-block;
        width: 280px;
        padding-top:3px;
    }
  
    .qlwz_search_div>.el-input,
    .qlwz_search_div>.el-select,
    .qlwz_search_div>.vue-treeselect{
        width: calc(100% - 130px) !important;
    }
  
    .qlwz_search_div label{
        display: inline-block;
        width:100px;
        text-align: right;
    }
    
  
    /***************  弹出开始  ********************/
    .el-dialog__header {
      /* 弹出窗口标题区域样式 */
      border-bottom:1px solid #EAEEF5;
      text-align: left;
    }
    
    .el-dialog {
      /* 弹出窗口整体最大高度 */
      max-height:calc(100vh - 170px);
      overflow: auto;
    }
    .el-dialog__body {
      /* 弹出窗口内容区域最大高度 */
      max-height:calc(100vh - 288px);
      overflow: auto; 
      padding: 10px 10px !important;
  
    }
  
    .qlwz-pop-search-div{
      border: 1px solid #EBEEF5;
    }
  
    .footer{
      text-align: center;
      position: absolute;
      bottom: 0px;
      background-color: #ffffff;
      width: calc(100% - 50px);
      margin:0px auto;
    }
  
  /***************  弹出结束  ********************/
  
  
  /***************  表格开始  ********************/
  
  /* 
    .el-table th.el-table__cell,.el-table th{
      background-color: rgb(227 240 253) !important;
    } */
  
    .el-table .cell{
      padding: 5px 2px 5px 2px;
    }
  
    
  /***************  表格开始  ********************/
  
  /***************  form  ********************/
  
      
    .qlwz-edit-container{
      /* 普通编辑页面整体背景 */
      background-color:#fcfcfc;
  
    }
    .qlwz-edit-formArea{
      /* 卡片页面内容区域 */
     /*  height:calc(100vh - 188px); */
     height: auto;
      overflow:auto;
      padding-bottom: 40px;
  
    }
    fieldset{
      border-radius: 5px;
      border:1px solid #cccccc;
      margin-bottom: 25px;
      padding: 20px;
      background-color: #ffffff;
    }
    legend{
      font-size: 16px;
      padding: 5px;
    }
  
    .el-form .el-input--small .el-input__inner,.el-form .el-range-editor--small .el-input__inner{
      height:42px !important;
      line-height: 42px !important;
      color: #606266 !important;
    }
  
  
    .el-range-editor.is-disabled input
    ,.el-range-editor.is-disabled .el-range-separator
    ,.el-date-editor .el-range__icon
    ,.el-textarea.is-disabled .el-textarea__inner{
      color: #606266 !important;
    }
  
    .form_view_span{
      display: inline-block;
      width: calc(100% - 10px);
      background-color: #F5F5F5;
      border-radius: 8px;
      padding:5px;
      min-height: 36px;
    }
  /*********  form  ********************/
  
    .qlwz-list-title{
      font-size: 16px;
      font-weight: bold;
      border-left: 3px solid #3572bf;
      padding: 0 10px;
      position: relative;
      left: -15px;
      padding-left: 20px；
    }
  
    .qlwz-list-searchArea label{
      width: 100px;
      display: inline-block;
      text-align: right
    }
    
    /********************************** 以上是综合部页面优化定义的样式 *********************************************/
    
    