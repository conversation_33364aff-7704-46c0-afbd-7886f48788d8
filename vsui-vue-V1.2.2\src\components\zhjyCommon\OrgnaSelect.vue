<template>
    <treeselect
            style="width: 160px;display: inline-block;height: 26px"
            :options="treeData"
            v-model= selectForm.currentValue
            @input="treeSelectValue"
            @select="treeSelectValuesel"
            :normalizer="normalizer"
            :default-expand-level="1"
            :searchable="false"
            :disabled=!isEdit
            :disableFuzzyMatching="disableFuzzyMatching"
            placeholder="请选择"
            :clearable="true"
            :multiple="multiple"
            @deselect="deselect"
            :flat="flat"
    ></treeselect>
</template>

<script>
    import treeselect from '@riophae/vue-treeselect'
    import '@riophae/vue-treeselect/dist/vue-treeselect.css'

    export default {
        components: {treeselect},
        props: {
            // 默认显示的值
            currentValue: String,
            // 判断是否 可编辑
            isEdit: {
                type: Boolean,
                default: true
            },
            // 是否支持多选
            multiple: {
                type: Boolean,
                default: false,
            },
            // 多选级联
            flat: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                selectForm: {
                    currentValue: null,
                },
                // 下拉树 数据
                treeData: [],
                normalizer(node) {
                    if (node.children == null) {
                        delete node.children;
                    }
                    return {
                        id: node.orgnaId,
                        label: node.orgnaName,
                        children: node.children,
                    }
                },
                disableFuzzyMatching:true
            }
        },
        mounted() {
            this.loadListData();
        },
        methods: {
            /**
             * @description: 加载树形结构数据
             * @param {*}
             * @return {*}
             */
            loadListData() {
                let treeData = {};
                this.axios({
                    method: "post",
                    url: "/backend/dmController/queryOrgTreeList",
                    data: treeData,
                }).then((resp) => {
                    if (resp.data.data.length > 0) {
                        this.treeData = resp.data.data;
                        if(this.currentValue && !this.multiple) {
                            this.selectForm.currentValue = this.currentValue;
                        } else if (this.currentValue && this.multiple){
                            this.selectForm.currentValue = this.currentValue.split(",");
                        } else {
                            this.selectForm.currentValue = null;
                        }
                    }
                });
            },
            /**
             * @description: 下拉树 选择后的 回调
             * @param {*}
             * @return {*}
             */
            treeSelectValue(data, node) {
              if (data == undefined){
                this.$emit("checkData", {});
              }
            },

          treeSelectValuesel(data, node){
            if (data != undefined){
              this.$emit("checkData", data);
            }
          },

            /**
             * 取消选择回调
             */
            deselect(node, instanceId) {
                this.$emit("deselect", node);
            },

        },
    }
</script>
