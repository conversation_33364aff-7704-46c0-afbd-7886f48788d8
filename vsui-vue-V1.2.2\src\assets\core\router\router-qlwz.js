
function checkLogined(to, userName, userPermission) {
    //获取用户名可以使用如下方法：
    //方式1：本函数参数会传入userName
    //方式2：VSAuth.getUserName();
    //return userName!=""

    if (userName == 'empty') {
        //如果是应用系统中不存在的账号
        return false;
    }
    return userName != "";
}
const routerQLWZDemo = [
    {
        path: "jdxs",
        name: "jdxs",
        props: { type: 'JDXS' },
        component: () => import("@components/page/qlwz/qlwzList.vue"),

        meta: { title: "监督写实表单", permission: checkLogined }
    },
    {
        path: "zrqhdjl",
        name: "zrqhdjl",
        props: { type: 'ZRQHD' },
        component: () => import("@components/page/qlwz/qlwzList.vue"),

        meta: { title: "责任区活动记录", permission: checkLogined }
    },
    {
        path: "sjzl",
        name: "sjzl",
        props: { type: 'SJZL' },
        component: () => import("@components/page/qlwz/qlwzList.vue"),

        meta: { title: "审计专栏", permission: checkLogined }
    },
    {
        path: "jfkt",
        name: "jfkt",
        props: { type: 'JFKT' },
        component: () => import("@components/page/qlwz/qlwzList.vue"),
        meta: { title: "纪法课堂", permission: checkLogined }
    },
    {
        path: "dzbsjsh",
        name: "dzbsjsh",
        props: { type: 'dzbsjsh' },
        component: () => import("@components/page/qlwz/sh/shList.vue"),
        meta: { title: "纪检委员审核", permission: checkLogined }
    },
    {
        path: "jjbsh",
        name: "jjbsh",
        props: { type: 'jjbsh' },
        component: () => import("@components/page/qlwz/sh/shList.vue"),
        meta: { title: "纪检部审核", permission: checkLogined }
    },
    {
        path: "glylb",
        name: "glylb",
        component: () => import("@components/page/qlwz/gly/glyList.vue"),
        meta: { title: "管理员列表", permission: checkLogined }
    },
    {
        path: "shwhList",
        name: "shwhList",
        component: () => import("@components/page/qlwz/shwh/shwhList.vue"),
        meta: { title: "审核维护列表", permission: checkLogined }
    },

]

export default routerQLWZDemo;
