<template>
    <div class="zhyy-list-container">
        <div class="zhyy-list-main">
            <el-row class="zhyy-list-searchArea" >
                <el-col :span="18">
                    <label >表单名称：</label>
                    <el-input @keyup.enter.native="processSearch" style="width: 200px;"
                        class="filter-item" placeholder="表单名称" v-model="listQuery.name" >
                    </el-input>

                    <label style="margin-left:8px;">表单ID：</label>
                    <el-input @keyup.enter.native="processSearch" style="width: 200px;"
                        class="filter-item" placeholder="表单ID" v-model="listQuery.id" >
                    </el-input>

                    <el-button type="primary" icon="el-icon-search" style="margin-left:8px;"
                        @click="processSearch">查询</el-button>
                </el-col>
                <el-col :span="6" style="text-align:right">
                    <el-button type="success" icon="el-icon-plus" @click="addData">新建</el-button>
                </el-col>
            </el-row>
            
            <el-row class="zhyy-list-tableArea" >
                <el-table 
                    v-loading="listLoading" 
                    highlight-current-row
                    :stripe="true" 
                    ref="listTable"
                    :data="tableData" 
                    :row-key="getRowKey"
                    :header-cell-style="{ background: '#F4F7FA'}"
                    @current-change="tableHandleCurrentChange"
                    >
                    
                    
                    <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px">
                    </el-table-column>
                    <el-table-column min-width="15%" align="left" header-align="center" label="列表选择">
                        <template slot-scope="scope">
                            <el-button v-if="scope.row.edit"  type="info" icon="el-icon-plus" @click="popSelect">选择</el-button>
                            <span>{{scope.row.SELECT}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="15%" align="left" header-align="center" label="文本框">
                        <template slot-scope="scope">
                            <el-input size="small" v-model="scope.row.NAME" v-if="scope.row.edit" placeholder="请输入"></el-input>
                            <span v-else>{{scope.row.NAME}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="8%" align="center" header-align="center"  label="下拉框">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.LBMC" 
                                v-if="scope.row.edit" 
                                @change="(systemId) => lbChange(systemId, scope.$index)" 
                                placeholder="请选择">
                                <el-option
                                    v-for="item in lxList"
                                    :key="item.id"
                                    :label="item.text"
                                    :value="item.id+'_'+item.text">
                                </el-option>
                            </el-select>
                            <span v-else>{{scope.row.LBMC}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="8%" align="left" header-align="center" label="下拉树">
                         <template slot-scope="scope">
                              <treeselect 
                                v-if="scope.row.edit" 
                                v-model="scope.row.TREEID"
                                :options="deptList" 
                                :normalizer="normalizer" 
                                :appendToBody="true"
                                :default-expand-level="3"
                                :clearable="false"
                                @select="(node,instanceId) => treeSelectValue(node,scope.$index)"
                                />
                            <span v-else>{{scope.row.TREENAME}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="8%" align="center" header-align="center" label="时间控件">
                         <template slot-scope="scope">
                            <el-date-picker 
                                v-if="scope.row.edit" 
                                v-model="scope.row.CRTIME"
                                type="date"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期">
                                </el-date-picker>
                            <span v-else>{{scope.row.CRTIME}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="8%" align="right" header-align="center" label="数字控件">
                         <template slot-scope="scope">
                            <el-input-number 
                                v-model="scope.row.NUMBER"
                                v-if="scope.row.edit"
                                controls-position="right"
                                :min="1" :max="100">
                                </el-input-number>
                            <span v-else>{{scope.row.NUMBER}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="10%" align="center" header-align="center" label="附件">
                         <template slot-scope="scope">
                             
                            <el-upload
                                name="files"
                                :disabled="scope.row.edit"
                                class="upload-demo"
                                :file-list="fileList"
                                action="/vscomponent/fileupload/upload"
                                accept=".jpeg,.jpg,.png,.doc,.docx,.pptx,.ppt,.xls,.xlsx"
                                :data="{busType: 'fileType', busId: scope.row.ID, standbyField0:'fileType'}"
                                >
                                <el-button size="small" type="primary">点击上传</el-button>
                            </el-upload>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="10%" label="操作" align="center" header-align="center" >
                        <template slot-scope="scope">
                            <el-button size="mini" type="text" icon="el-icon-delete"
                                @click="deleteData(scope.$index, scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="zhyy-list-paginationArea">
                    <el-pagination background 
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange" 
                        :page-sizes="[10, 20, 30, 50]" 
                        :page-size="listQuery.pageSize"
                        :current-page="listQuery.pageIndex"
                        layout="total, sizes, prev, pager, next, jumper" 
                        :total="total"
                        ></el-pagination>
                </div>
            </el-row>

        </div>

        <!-- 以下是简单弹出页 -->
        <el-dialog  v-dialogDrag 
            v-if="popFormVisable"
            title="简单弹出页" 
            width="880px"
            :visible.sync="popFormVisable" 
            :close-on-click-modal="false" >
            <popForm 
            @confirmSelect="handleClosePopFormDialog" 
            @clearSelect="handleClosePopFormDialog" 
            />
        </el-dialog>
    </div>
</template>

<script>
import popForm from "./zhyy-pop";
import { v4 as getUUID } from 'uuid';
import treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
    name: "zhyy_lineedit_list",
    
    components: {
        popForm,
        treeselect
    },

    data() {
        return {
            total: null,
            listLoading: true,
            tableData: undefined,
            currentRow:null,
            pkColumn: 'ID',
            listQuery: {
                pageIndex: 1,
                pageSize: 10,
                name: "",
                id: "",
            },
            popFormVisable: false,
            lxList:[
                {id:'1',text:'下拉1'},
                {id:'2',text:'下拉2'},
            ],
            deptList:[{
                id:'1',
                text:'父级',
                children:[
                    {id:'2',text:'子集1'},
                    {id:'3',text:'子集2'}
                ]
            }],
            normalizer(node) {
                return {
                    id: node.id,
                    label: node.text
                }
            },
             //附件列表
            fileList: [],
        };
    },
    mounted() {
        this.loadListData();

    },
    methods: {
        /**
		 * @description: 加载列表数据
		 * @param {*}
		 * @return {*}
		 */
        loadListData() {
            this.listLoading = false;
            this.tableData = [
                {
                    ID:'1',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBID:'1',
                    LBMC:'下拉1',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09',
                    NUMBER:1,
                    TREEID:'2',
                    TREENAME:'子集1',
                    SELECT:'选择数据1',
                },
                {
                    ID:'2',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBID:'2',
                    LBMC:'下拉2',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09',
                    NUMBER:2,
                    TREEID:'3',
                    TREENAME:'子集2',
                    SELECT:'选择数据1,选择数据2',
                },
                {
                    ID:'3',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBID:'1',
                    LBMC:'下拉1',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09',
                    NUMBER:3,
                    TREEID:'2',
                    TREENAME:'子集1',
                    SELECT:'选择数据2',
                },
                {
                    ID:'4',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBID:'2',
                    LBMC:'下拉2',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09',
                    NUMBER:4,
                    TREEID:'1',
                    TREENAME:'父级',
                    SELECT:'选择数据3',
                },

            ];

            this.total = 4;
        },

        /**
		 * @description: 查询数据，需要把页数重置为1
		 * @param {*}
		 * @return {*}
		 */
        processSearch() {
            this.listQuery.pageIndex = 1;
            this.loadListData();
        },

        /**
		 * @description: 变更每页条数时自动加载数据
		 * @param {*}
		 * @return {*}
		 */
        handleSizeChange(val) {
            this.listQuery.pageSize = val;
            this.loadListData();
        },
        
        /**
		 * @description: 点击某一页时自动加载数据
		 * @param {*}
		 * @return {*}
		 */
        handleCurrentChange(val) {
            this.listQuery.pageIndex = val;
            this.loadListData();
        },


        /**
		 * @description: 打开简单的弹出选择页
		 * @param {*}
		 * @return {*}
		 */
        popSelect() {
            this.popFormVisable = true;
        },


        /**
		 * @description: 打开新增页面 - 弹出方式
		 * @param {*}
		 * @return {*}
		 */
        addData() {
            this.tableData.unshift({ID:getUUID().replace(/-/g, '').toUpperCase(),edit:true});
            this.$refs.listTable.setCurrentRow(this.tableData[0]);
        },


        /**
		 * @description: 删除数据
		 * @param {*}
		 * @return {*}
		 */
        deleteData(index, row) {

            this.$confirm("此操作将永久删除当前数据，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
            .then(() => {
               //执行删除

            })
            .catch(err => {
            });
        },

        getRowKey(rowData){
            return rowData[this.pkColumn];
        }, 
         /**
		 * @description: 行单机事件
		 * @param {currentRow:'当前选择行',oldCurrentRow:'之前选中行'}
		 * @return {*}
		 */
        tableHandleCurrentChange(currentRow,oldCurrentRow){
            if(currentRow){
                currentRow.edit = true;
            }
            if(oldCurrentRow){
                oldCurrentRow.edit = false;
            }
            this.currentRow = currentRow;
        },

        //下拉菜单选择事件
        lbChange(val,index){
            if(val&&val.indexOf('_')>0){
                let applicationArr = val.split('_');
                this.tableData[index]['LBID'] = applicationArr[0];
                this.tableData[index]['LBMC'] = applicationArr[1];
            }
        },
        //树下拉选择事件
        treeSelectValue(node,index){
            if(node && node.id){
                this.tableData[index]['TREEID'] = node.id;
                this.tableData[index]['TREENAME'] = node.text;
            }
            
        },
        //行选择列表
        handleClosePopFormDialog(rows){
            if(rows && rows.length>0){
                let SELECTS = '';
                for(let i=0;i<rows.length;i++){
                    if(i==0){
                        SELECTS = rows[i]['NAME'];
                    }else{
                        SELECTS += ',' + rows[i]['NAME'];
                    }
                }
                this.currentRow.SELECT = SELECTS;
            }else{
                this.currentRow.SELECT = '';
            }
            this.popFormVisable = false;
        }

    },
    
};

</script>


<style scope>

</style>