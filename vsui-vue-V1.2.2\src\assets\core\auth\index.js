/**
 * 20210324 by cuiliang 
 * 
 * 框架用户信息及鉴权信息入口，
 * 
 * 应事业部需要，增加用户信息中间层，而非直接在框架中引入胜软鉴权组件，便于项目不使用权限中心时，提供框架对用户身份信息的基础埋点
 * 开发者需要按照以下说明提供数据返回，则可顺利并正常的在框架内自动显示
 *
 * 框架默认使用import VSAuth from "./vsAuth.js";
 * 引入胜软鉴权组件(@vsui/lib-vueauth4vseaf)，
 * 并提供一整套登录退出以及各种鉴权模式的库以及插件，
 * 
 * 
 * 
 */
 import {Cookie} from "@vsui/lib-jsext";
 import version from "../version";

 
 
export default{

     instance:(function(){
        var VSAuth=undefined;
        var Return={

            init(vsAuth){
                if(VSAuth){
                    console.warn(`${version}:鉴权组件重新赋值！`)
                }
                VSAuth=vsAuth;
            },

            /**
                 * 必须实现
                 * 当前登录用户名字符串
                 * 数据示例："admin",
                 */
            getUserName(){
                return VSAuth.getAuthInfo().userName
            },
            /**
             * 非必须实现 
             * 用户的权限完整信息，JSON对象,结构自定义,框架中仅做保存，未使用，以备业务需要
             * 数据示例:{权限JSON对象,结构自定义,框架中仅做保存，未使用，以备业务需要}
             */
            getPermission(){
                return VSAuth.getAuthInfo().permission
            },
            /**
             * 
             * 必须实现
             * 用户资源转换为树状菜单后的对象
             * 结构如下：
             *      [{
            *           resId:"菜单唯一识别符,必选",
            *           iconColor:"#000,代表菜单图标字体颜色，可选",
            *           iconClass:"fa fa-bus 字体字库名称，代表菜单图标，可选",
            *           resPvalue:"菜单url路径，第一级非必选，二级及以下菜单必选",
            *           resName:"菜单名称文字",
            *           children:[{
                            resId:"菜单唯一识别符,必选",
            *               iconColor:"#000,代表菜单图标字体颜色，可选",
            *               iconClass:"fa fa-bus 字体字库名称，代表菜单图标，可选",
            *               resPvalue:"菜单url路径，第一级非必选，二级及以下菜单必选",
            *               resName:"菜单名称文字"},
            *               {...},
            *               {...}]
            *          }],
            */
            getModulesTree(){
                return VSAuth.getAuthInfo().modulesTree
            },

            /**
             * 非必须
             * 为使用jwt等模式的登陆时预留
             * 在REST无会话模式下使用的票据信息
             */
            getToken(){
                return VSAuth.getAuthInfo().token
            },


            /**
             * 登录函数指向，框架自带了权限中心组件，默认如下：
             * 返回为一个Promise对象
             * login:(参数按需定义即可)=>{
             *  return new Promise((resolve,reject)=>{
             *          //todo 这里是登录过程
             *      });
             * }
             * @param {string} username 用户名
             * @param {string} password 密码
             * @returns {Promise} 返回一个异步对象
             */
            login:(username,password)=>{
                return new Promise((resolve,reject)=>{
                    VSAuth.getAuthInfo().auth.login(username,password).then(()=>{
                        if(//VSAuth.getConfig().authMode=="SECURITY"||
                        VSAuth.getConfig()?.authMode=="DEV")
                        {
                            Cookie.Cookie.addCookie(Cookie.CookieDef.userName,username,Cookie.CookieDef.hours,Cookie.CookieDef.path,Cookie.CookieDef.domain)
                            Cookie.Cookie.addCookie(Cookie.CookieDef.passwd,password,Cookie.CookieDef.hours,Cookie.CookieDef.path,Cookie.CookieDef.domain)
                        }
                        resolve();
                    }).catch(error=>{
                        reject(error);
                    })
                })
            },

            /**
             * 退出函数指向，框架自带了权限中心组件，默认如下：
             * 返回为一个Promise对象
             * logout:(参数按需定义即可)=>{
             *  return new Promise((resolve,reject)=>{
             *          //todo 这里是退出过程
             *      });
             * }
             * @returns {Promise} 返回一个异步对象
             * 
             */
            logout:()=>{
                return new Promise((resolve,reject)=>{
                    VSAuth.getAuthInfo().auth.logout().then(()=>{
                        if(//VSAuth.getConfig().authMode=="SECURITY"||
                        VSAuth.getConfig()?.authMode=="DEV")
                        {
                            Cookie.Cookie.delCookie(Cookie.CookieDef.userName,Cookie.CookieDef.path,Cookie.CookieDef.domain);
                            Cookie.Cookie.delCookie(Cookie.CookieDef.passwd,Cookie.CookieDef.path,Cookie.CookieDef.domain);
                            VSAuth.getAuthInfo().auth.toLoginPage()
                        }
                        resolve();
                    }).catch(error=>{
                        reject(error)
                    });
                })
            },
        };
        return Return;
    })(),

   

    
}