
/**
 *  @author:cuiliang 
 *  @email: <EMAIL>
 *  @date：20210527
 *  @version:V1.2.0
 *  @description: 默认向vue组件（页面）中混入的内容，请在vue中使用
 *  import mixin from "该文件地址"
 *  export default{
 *      mixins:[mixin],
 *  }
 * 
 */
import auth from "../auth";
import axios from "../axios";
import router from "../router";
import runtimeCfg  from "../runtime/config";


export default {
    name: "vsui.vue@1.2.0_mixin",
    data(){
        return {
            axios,
            eventbus:this.$EventBus,
            router,
			auth,
		   /**
			 * 前端的基准路径，如果在html模板中使用绝对路径引入图片，需要加上此路径
			 * 如前提：
			 *  1：图片物理路径为"static\img\themes\green\theme.jpg";
			 *  2：应用的发布路径为"/vs-cloud/"，即src\config.js中配置的publicRootPath;
			 *  3：当前文件路径为："src\views\Dashboard.vue";
			 * 则发布后的图片访问地址应为"/vs-cloud/static/img/themes/green/theme.jpg",
			 * 即：
			 *  使用绝对路径的情况下代码如下：<img :src="basePath+'static/img/themes/green/theme.jpg'">
			 *  使用相对路径的情况下代码如下：<img src="../../static/img/themes/green/theme.jpg">
			 * 切勿：
			 *  1：在使用绝对路径的情况下直接使用"/"开头写入图片路径，这样在应用增加发布路径时会导致大量的资源路径修改工作
			 * 如：
			 * 1：页面中使用import引入图片资源，或
			 * 2：CSS中使用相对路径引入的图片，或
			 * 均不需要使用此值
			 * @returns 返回前端程序的基准路径
			 */
			basePath : process.env.NODE_ENV === 'production'
					? runtimeCfg.app_public_path : "/" ,

			restBasePath:runtimeCfg.rest_base_path,

        }
    },
    computed:{
    },
    methods:{
        
    },

}