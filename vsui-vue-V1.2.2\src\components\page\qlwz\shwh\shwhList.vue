<template>
  <div class="zhyy-list-container">
    <div class="zhyy-list-main">
      <div class="zhyy-list-searchArea">
        <el-row>
          <el-col :span="22" style="display: flex; align-items: center;">
            <div style="display: flex; align-items: center;">
              <label style="width: 90px;">审核人账号：</label>
              <el-input @keyup.enter.native="processSearch" style="width: 200px;" clearable
                class="filter-item" placeholder="请输入审核人账号" v-model="listQuery.SHRZH" >
              </el-input>
            </div>
            <div style="display: flex; align-items: center;">
              <label style="width: 100px;">审核人名称：</label>
              <el-input @keyup.enter.native="processSearch" style="width: 200px;" clearable
                class="filter-item" placeholder="请输入审核人名称" v-model="listQuery.SHRMC" >
              </el-input>
            </div>
            <div style="display: flex; align-items: center; margin-left: 30px;">
              <el-button type="primary" icon="el-icon-search" @click="processSearch()">查询</el-button>
            </div>
          </el-col>
          <el-col :span="2" style="text-align:right">
            <el-button type="success" icon="el-icon-plus" @click="toEditData()">新建</el-button>
          </el-col>
        </el-row>
      </div>
        
      <el-row class="zhyy-list-tableArea" >
        <el-table :stripe="true" v-loading="listLoading" highlight-current-row
          ref="listTable" :data="tableData" :row-key="getRowKey" :header-cell-style="{ background: '#F4F7FA'}">
            <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px"></el-table-column>
            <el-table-column min-width="18%" align="left" header-align="center" label="审核人名称">
              <template slot-scope="scope">
                <span>{{scope.row.SHRMC}}</span>
              </template>
            </el-table-column>
            <el-table-column min-width="5%" align="center" header-align="center" label="审核人账号">
              <template slot-scope="scope">
                <span>{{scope.row.SHRZH}}</span>
              </template>
            </el-table-column>
            <el-table-column min-width="15%" align="left" header-align="center"  label="审核单位">
              <template slot-scope="scope">
                <span>{{scope.row.SHDWMC}}</span>
              </template>
            </el-table-column>
            <el-table-column min-width="8%" align="center" header-align="center" label="创建时间">
              <template slot-scope="scope">
                <span>{{scope.row.CJSJ}}</span>
              </template>
            </el-table-column>
            <el-table-column min-width="16%" label="操作" align="center" header-align="center" >
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="toEditData(scope.row, 'edit')">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteData(scope.$index, scope.row)">删除</el-button>
              </template>
            </el-table-column>
        </el-table>
      </el-row>
    </div>

    <el-dialog v-dialogDrag 
      v-if="getSubmitViewVisable"
      title="审核维护编辑" 
      width="60vw"
      :visible.sync="getSubmitViewVisable" 
      :before-close="handleCloseViewDialog" 
      :close-on-click-modal="false" >
        <shwhEdit
          :shwhEditData="shwhEditData" 
          :operation="operation"
          @handleSubmitSuccess="handleSubmitSuccess"  
          @handleCloseViewDialog="handleCloseViewDialog"/>
    </el-dialog>

  </div>
</template>

<script>
import shwhEdit from "./shwhEdit";
export default {
  name: "shwhList",
  components: {
    shwhEdit
  },
  data() {
    return {
      listLoading: true,
      tableData: undefined,
      pkColumn: 'PZID',
      listQuery: {
        SHRZH: "",
        SHRMC: "",
      },
      operation: '',
      shwhEditData: {},
      getSubmitViewVisable: false,
    };
  },

  mounted() {
    this.loadListData();
  },

  methods: {
    /**
     * @description: 加载列表数据
     * @param {*}
     * @return {*}
     */
    loadListData() {
      this.listLoading = false;
      this.axios({
        url: "/backend/shwhController/selectShwhList",
        method: "post",
        data: {
          ...this.listQuery
        },
      })
      .then((resp) => {
        this.tableData = resp.data.data
      })
      .catch((error) => {
        this.$message.error(error);
      });
    },

    /**
     * @description: 查询数据，需要把页数重置为1
     * @param {*}
     * @return {*}
     */
    processSearch() {
      this.loadListData();
    },
      
    /**
     * @description: 删除数据
     * @param {*}
     * @return {*}
     */
    deleteData(index, row) {
      console.log(JSON.stringify(row));

      this.$confirm("此操作将永久删除当前数据，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
      .then(() => {
        this.axios({
          url: "/backend/shwhController/deleteOneShwh",
          method: "post",
          data: {
            PZID: row.PZID,
          },
        })
        .then((resp) => {
          this.$notify({
            title: "提示",
            message: "删除成功！",
            type: "success",
            duration: 2000,
            offset: 80
          });
          this.loadListData();
        })
        .catch((error) => {
          this.$message.error(error);
        });
      })
      .catch(err => {
      });
    },

    /**
    * @description: 弹出页面提交后回调函数
    * @param {*}
    * @return {*}
    */
    handleSubmitSuccess() {
      this.getSubmitViewVisable = false;
      this.loadListData();
    },

    /**
    * @description: 弹出页面点击关闭按钮回调函数
    * @param {*}
    * @return {*}
    */
    handleCloseViewDialog() {
      this.getSubmitViewVisable = false;
    },
      
    /**
    * @description: 打开简单的弹出选择页
    * @param {*}
    * @return {*}
    */
    toEditData(row, item) {
      if (!row) {
        // 新建
        this.operation = 'add'
      } else {
        // 编辑或查看
        this.operation = item;
        this.shwhEditData = row;
      }
      this.getSubmitViewVisable = true;
    },

    getRowKey(rowData){
      return rowData[this.pkColumn];
    },
  },
};

</script>

<style scoped>

</style>