<template>
  <div class="zhyy-list-container">
    <div class="zhyy-list-main">
      <div class="zhyy-list-searchArea">
        <el-row>
          <el-col :span="22" style="display: flex; align-items: center;">
            <div style="display: flex; align-items: center;">
              <label style="width: 90px;">审核人账号：</label>
              <el-input @keyup.enter.native="processSearch" style="width: 200px;" clearable
                class="filter-item" placeholder="请输入审核人账号" v-model="listQuery.SHRZH" >
              </el-input>
            </div>
            <div style="display: flex; align-items: center;">
              <label style="width: 100px;">审核人名称：</label>
              <el-input @keyup.enter.native="processSearch" style="width: 200px;" clearable
                class="filter-item" placeholder="请输入审核人名称" v-model="listQuery.SHRMC" >
              </el-input>
            </div>
            <div style="display: flex; align-items: center; margin-left: 30px;">
              <el-button type="primary" icon="el-icon-search" @click="processSearch()">查询</el-button>
            </div>
          </el-col>
          <el-col :span="2" style="text-align:right">
            <el-button type="success" icon="el-icon-plus" @click="toEditData()">新建</el-button>
          </el-col>
        </el-row>
      </div>
        
      <el-row class="zhyy-list-tableArea" >
        <el-table :stripe="true" v-loading="listLoading" highlight-current-row
          ref="listTable" :data="tableData" :row-key="getRowKey" :header-cell-style="{ background: '#F4F7FA'}">
            <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px"></el-table-column>
            <el-table-column min-width="18%" align="left" header-align="center" label="审核人名称">
              <template slot-scope="scope">
                <span>{{scope.row.SHRMC}}</span>
              </template>
            </el-table-column>
            <el-table-column min-width="5%" align="center" header-align="center" label="审核人账号">
              <template slot-scope="scope">
                <span>{{scope.row.SHRZH}}</span>
              </template>
            </el-table-column>
            <el-table-column min-width="15%" align="left" header-align="center"  label="审核单位">
              <template slot-scope="scope">
                <span>{{scope.row.SHDWMC}}</span>
              </template>
            </el-table-column>
            <el-table-column min-width="8%" align="center" header-align="center" label="创建时间">
              <template slot-scope="scope">
                <span>{{scope.row.CJSJ}}</span>
              </template>
            </el-table-column>
            <el-table-column min-width="16%" label="操作" align="center" header-align="center" >
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="toEditData(scope.row, 'edit')">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteData(scope.$index, scope.row)">删除</el-button>
              </template>
            </el-table-column>
        </el-table>
      </el-row>
    </div>

    <el-dialog v-dialogDrag 
      v-if="getSubmitViewVisable"
      title="审核维护编辑" 
      width="60vw"
      :visible.sync="getSubmitViewVisable" 
      :before-close="handleCloseViewDialog" 
      :close-on-click-modal="false" >
        <shwhEdit
          :shwhEditData="shwhEditData" 
          :operation="operation"
          @handleSubmitSuccess="handleSubmitSuccess"  
          @handleCloseViewDialog="handleCloseViewDialog"/>
    </el-dialog>

  </div>
</template>

<script>
import shwhEdit from "./shwhEdit";
export default {
  name: "shwhList",
  components: {
    shwhEdit
  },
  data() {
    return {
      listLoading: true,
      // tableData: undefined,
      tableData: [
        {
          PZID: "001",
          SHRMC: "张三",
          SHRZH: "zhangsan001",
          SHDWMC: "财务部",
          CJSJ: "2024-01-15"
        },
        {
          PZID: "002",
          SHRMC: "李四",
          SHRZH: "lisi002",
          SHDWMC: "人事部",
          CJSJ: "2024-01-16"
        },
        {
          PZID: "003",
          SHRMC: "王五",
          SHRZH: "wangwu003",
          SHDWMC: "技术部",
          CJSJ: "2024-01-17"
        },
        {
          PZID: "004",
          SHRMC: "赵六",
          SHRZH: "zhaoliu004",
          SHDWMC: "市场部",
          CJSJ: "2024-01-18"
        },
        {
          PZID: "005",
          SHRMC: "钱七",
          SHRZH: "qianqi005",
          SHDWMC: "销售部",
          CJSJ: "2024-01-19"
        },
        {
          PZID: "006",
          SHRMC: "孙八",
          SHRZH: "sunba006",
          SHDWMC: "采购部",
          CJSJ: "2024-01-20"
        },
        {
          PZID: "007",
          SHRMC: "周九",
          SHRZH: "zhoujiu007",
          SHDWMC: "质量部",
          CJSJ: "2024-01-21"
        },
        {
          PZID: "008",
          SHRMC: "吴十",
          SHRZH: "wushi008",
          SHDWMC: "生产部",
          CJSJ: "2024-01-22"
        },
        {
          PZID: "009",
          SHRMC: "郑一一",
          SHRZH: "zhengyiyi009",
          SHDWMC: "研发部",
          CJSJ: "2024-01-23"
        },
        {
          PZID: "010",
          SHRMC: "王二二",
          SHRZH: "wangerer010",
          SHDWMC: "法务部",
          CJSJ: "2024-01-24"
        },
        {
          PZID: "011",
          SHRMC: "李三三",
          SHRZH: "lisansan011",
          SHDWMC: "行政部",
          CJSJ: "2024-01-25"
        },
        {
          PZID: "012",
          SHRMC: "张四四",
          SHRZH: "zhangsisi012",
          SHDWMC: "客服部",
          CJSJ: "2024-01-26"
        },
        {
          PZID: "013",
          SHRMC: "刘五五",
          SHRZH: "liuwuwu013",
          SHDWMC: "物流部",
          CJSJ: "2024-01-27"
        },
        {
          PZID: "014",
          SHRMC: "陈六六",
          SHRZH: "chenliuliu014",
          SHDWMC: "安全部",
          CJSJ: "2024-01-28"
        },
        {
          PZID: "015",
          SHRMC: "杨七七",
          SHRZH: "yangqiqi015",
          SHDWMC: "审计部",
          CJSJ: "2024-01-29"
        },
        {
          PZID: "016",
          SHRMC: "黄八八",
          SHRZH: "huangbaba016",
          SHDWMC: "企划部",
          CJSJ: "2024-01-30"
        },
        {
          PZID: "017",
          SHRMC: "林九九",
          SHRZH: "linjiujiu017",
          SHDWMC: "培训部",
          CJSJ: "2024-01-31"
        },
        {
          PZID: "018",
          SHRMC: "徐十十",
          SHRZH: "xushishi018",
          SHDWMC: "信息部",
          CJSJ: "2024-02-01"
        },
        {
          PZID: "019",
          SHRMC: "朱一二",
          SHRZH: "zhuyier019",
          SHDWMC: "监察部",
          CJSJ: "2024-02-02"
        },
        {
          PZID: "020",
          SHRMC: "马二三",
          SHRZH: "maersan020",
          SHDWMC: "综合部",
          CJSJ: "2024-02-03"
        }
      ],
      pkColumn: 'PZID',
      listQuery: {
        SHRZH: "",
        SHRMC: "",
      },
      operation: '',
      shwhEditData: {},
      getSubmitViewVisable: false,
    };
  },

  mounted() {
    this.loadListData();
  },

  methods: {
    /**
     * @description: 加载列表数据
     * @param {*}
     * @return {*}
     */
    loadListData() {
      this.listLoading = false;
      this.axios({
        url: "/backend/shwhController/selectShwhList",
        method: "post",
        data: {
          ...this.listQuery
        },
      })
      .then((resp) => {
        this.tableData = resp.data.data
      })
      .catch((error) => {
        this.$message.error(error);
      });
    },

    /**
     * @description: 查询数据，需要把页数重置为1
     * @param {*}
     * @return {*}
     */
    processSearch() {
      this.loadListData();
    },
      
    /**
     * @description: 删除数据
     * @param {*}
     * @return {*}
     */
    deleteData(index, row) {
      console.log(JSON.stringify(row));

      this.$confirm("此操作将永久删除当前数据，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
      .then(() => {
        this.axios({
          url: "/backend/shwhController/deleteOneShwh",
          method: "post",
          data: {
            PZID: row.PZID,
          },
        })
        .then((resp) => {
          this.$notify({
            title: "提示",
            message: "删除成功！",
            type: "success",
            duration: 2000,
            offset: 80
          });
          this.loadListData();
        })
        .catch((error) => {
          this.$message.error(error);
        });
      })
      .catch(err => {
      });
    },

    /**
    * @description: 弹出页面提交后回调函数
    * @param {*}
    * @return {*}
    */
    handleSubmitSuccess() {
      this.getSubmitViewVisable = false;
      this.loadListData();
    },

    /**
    * @description: 弹出页面点击关闭按钮回调函数
    * @param {*}
    * @return {*}
    */
    handleCloseViewDialog() {
      this.getSubmitViewVisable = false;
    },
      
    /**
    * @description: 打开简单的弹出选择页
    * @param {*}
    * @return {*}
    */
    toEditData(row, item) {
      if (!row) {
        // 新建
        this.operation = 'add'
      } else {
        // 编辑或查看
        this.operation = item;
        this.shwhEditData = row;
      }
      this.getSubmitViewVisable = true;
    },

    getRowKey(rowData){
      return rowData[this.pkColumn];
    },
  },
};

</script>

<style scoped>

</style>