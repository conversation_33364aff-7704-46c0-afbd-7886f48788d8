<template>
    <div style="height:100%;background-color:#fff">
        <div style="height:370px;width: 800px;margin: auto;padding-top: 50px;">
            <quill-editor
                v-model="content"
                style="height:300px"
                ref="myQuillEditor"
                :options="editorOption"
                @blur="onEditorBlur($event)"
                @focus="onEditorFocus($event)"
                @change="onEditorChange($event)"
                @ready="onEditorReady($event)">
            </quill-editor>
        </div>
        <div style="text-align: center;">
            <el-button type="primary" @click="getText">获取文本内容</el-button>
            <el-button type="success" @click="getHtml">获取HEML内容</el-button>
        </div>
        
    </div>
</template>
<script>
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
export default {
    components: {
        quillEditor
    },
    data() {
        return {
            content:"",
            editorOption: {
                modules: {
                    toolbar: [
                            ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线 -----['bold', 'italic', 'underline', 'strike']
                            //["blockquote", "code-block"], // 引用  代码块-----['blockquote', 'code-block']
                            [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表-----[{ list: 'ordered' }, { list: 'bullet' }]
                            // [{ 'header': 1 }, { 'header': 2 }],
                            [{ script: "sub" }, { script: "super" }], // 上标/下标-----[{ script: 'sub' }, { script: 'super' }]
                            [{ indent: "-1" }, { indent: "+1" }],
                            [{ size: ['small', false, 'large', 'huge']}], // 配置字号
                            [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题-----[{ header: [1, 2, 3, 4, 5, 6, false] }]
                            //[{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色-----[{ color: [] }, { background: [] }]
                            //[{ font: []}], //显示字体选择
                            [{ align: [] }], // 对齐方式-----
                            //["clean"], // 清除文本格式-----
                            ['link', 'image']
                        ]
                },
                placeholder: '请输入正文'
            },

        }
    },
    props: {
    },
    mounted() {

    },
    methods: {
        getText(){
            alert(this.$refs.myQuillEditor.quill.getText());
        },
        getHtml(){
            alert(this.content);
        },
        // 失去焦点事件
        onEditorBlur(quill) {
            console.log('editor blur!', quill)
        },
        // 获得焦点事件
        onEditorFocus(quill) {
            console.log('editor focus!', quill)
        },
        // 准备富文本编辑器
        onEditorReady(quill) {
            console.log('editor ready!', quill)
        },
        // 内容改变事件
        onEditorChange({ quill, html, text }) {
            console.log('editor change!', quill, html, text)
            this.content = html
        },
    }
}
</script>

<style scoped>

</style>