import runtimeCfg               from            "./runtime/config";
import console                  from            "./console";
import {NameSpace,namespace}    from            "./namespace";
import Vue                      from            "./vue";
import auth                     from            "./auth"
import router                   from            "./router";
import                                          "./element-ui"; 
import mixin                    from            "./mixin";
import store                    from            "./store";
import axios                    from            "./axios";
import                                          "./event-bus/";
import version                  from            "./version";

process.env.NODE_ENV === 'production'?__webpack_public_path__ = runtimeCfg.app_public_path:undefined;

let _auth = NameSpace.getNameSpace(`${namespace}.auth`);
if(!_auth){NameSpace.setNameSpace(`${namespace}.auth`,auth)}

let _router = NameSpace.getNameSpace(`${namespace}.router`);
if(!_router){NameSpace.setNameSpace(`${namespace}.router`,router)}

let _store = NameSpace.getNameSpace(`${namespace}.store`);
if(!_store){NameSpace.setNameSpace(`${namespace}.store`,store)}

let _axios = NameSpace.getNameSpace(`${namespace}.axios`);
if(!_axios){NameSpace.setNameSpace(`${namespace}.axios`,axios)}

let _console = NameSpace.getNameSpace(`${namespace}.console`);
if(!_console){NameSpace.setNameSpace(`${namespace}.console`,console)}


/**
 * @author:cuiliang 
 * @email: <EMAIL>
 * @date：20210918
 * @version:V1.2.2
 * @description:
 * 导出所有核心库,框架使用核心库的地方均通过"src/assets/core/index.js"文件引入，禁止直接访问核心库内部的其他库文件
 * 本文件相关联上下文文件为胜软前端框架vsui.vue@1.2.2之部分内容，著作权属于胜软公司以及其创作人崔良所有，
 * 禁止将本文件上下文文件或部分内容脱离框架vsui.vue@1.2.2使用
 * 
 * 
 * 
 */
export {Vue,router,store,axios,console,auth,mixin,NameSpace,namespace,version,runtimeCfg}