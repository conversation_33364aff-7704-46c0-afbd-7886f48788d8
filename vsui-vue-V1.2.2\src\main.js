
import '@babel/polyfill'; 
import App from "./App.vue";
import "./lib/vsAuth";
import axios from 'axios';
import {Vue,router,store,NameSpace} from "./assets/core";
import VideoPlayer from 'vue-video-player'
import 'video.js/dist/video-js.css'
import 'vue-video-player/src/custom-theme.css'
Vue.prototype.axios = axios;




//视频播放器
Vue.use(VideoPlayer);
new Vue({
    router,
    store,
    render: h => h(App)
}).$mount("#app");




