'use strict'
const path = require('path')
const utils = require('./utils')
const webpack = require('webpack')
const buildconfig = require('../config/index.js')
const merge = require('webpack-merge')
const baseWebpackConfig = require('./webpack.base.conf')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const OptimizeCSSPlugin = require('optimize-css-assets-webpack-plugin')
const TerserPlugin=require('terser-webpack-plugin')
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const env = require('../config/prod.env')

const webpackConfig = merge(baseWebpackConfig, {
  mode:"production",
  entry: {
    app: ['./src/main.js']
  },
  module: {
    rules: utils.styleLoaders({
      sourceMap: buildconfig.build.productionSourceMap,
      extract: true,
      usePostCSS: true
    })
  },
  devtool: buildconfig.build.productionSourceMap ? buildconfig.build.devtool : false,
  output: {
    path: buildconfig.build.assetsRoot,
    filename: utils.assetsPath('js/[name].[chunkhash].js'),
    chunkFilename: utils.assetsPath('js/[id].[chunkhash].js')
  },
  plugins: [
    // http://vuejs.github.io/vue-loader/en/workflow/production.html
    new webpack.DefinePlugin({
      'process.env': env
    }),
    
    // Compress extracted CSS. We are using this plugin so that possible
    // duplicated CSS from different components can be deduped.
    // WEBPACK 4中使用MiniCssExtractPlugin替代ExtractTextWebpackPlugin
    new MiniCssExtractPlugin({
      filename: utils.assetsPath('css/[name].[contenthash].css'),
      chunkFilename: utils.assetsPath('css/[name].[contenthash].css')
    }),
    // generate dist index.html with correct asset hash for caching.
    // you can customize output by editing /index.html
    // see https://github.com/ampedandwired/html-webpack-plugin
    new HtmlWebpackPlugin({
      filename: buildconfig.build.index,
      template: 'index.html',
      //这里需要将插件的自动输出关掉，不然写入页面的脚本与样式路径不会按照\static\js\runtime\config.js配置内容自动更新，
      //此配置项配合模板页index.html内的自执行脚本使用
      inject: false,
      minify: {
        collapseWhitespace: false,
        keepClosingSlash: true,
        removeComments: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: false,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true,
      },
      templateParameters: (compilation, assets, assetTags, options) => {
           return {
              compilation,
              webpackConfig: compilation.options,
              htmlWebpackPlugin: {
                  tags: assetTags,
                  files: assets,
                  options,
              },
              prod:true, 
              version:"RELEASE-V"+(new Date()).format("yyyyMMddhhmmss"),
          }
      },
      // necessary to consistently work with multiple chunks via CommonsChunkPlugin
      chunksSortMode: 'dependency'
    }),
    // keep module.id stable when vender modules does not change
    new webpack.HashedModuleIdsPlugin(),
    // enable scope hoisting
    new webpack.optimize.ModuleConcatenationPlugin(),
    // copy custom static assets
    new CopyWebpackPlugin([
      {
        from: path.resolve(__dirname, '../static'),
        to: buildconfig.build.assetsSubDirectory,
        ignore: ['.*']
      },
      {
        from: path.resolve(__dirname, '../apidata'),
        to: buildconfig.build.assetsApiDataDirectory,
        ignore: ['']
      },
    ])
  ],
  optimization: {
    minimize:true,
    minimizer: [
      new OptimizeCSSPlugin({
        cssProcessorOptions: buildconfig.build.productionSourceMap
          ? { safe: true, map: { inline: false } }
          : { safe: true }
      }),
      new TerserPlugin({
        test: /\.js(\?.*)?$/i,
      }),
    ],
    splitChunks: {
      chunks: 'async',  // 分离异步代码（import()） initial 同步代码  all 所有
      minSize: 20000,   // 超过0B分离
      maxSize: 30000,
      minChunks: 3,     // 有X个文件引用就分离
      maxAsyncRequests: 5,          // 按需加载时最大并行请求数
      maxInitialRequests: 3,        // 入口点的最大并行请求数
      automaticNameDelimiter: '-',  // 分离出来的包的名字分隔符
      automaticNameMaxLength: 30,
      name: true, //拆分出来块的名字(Chunk Names)，默认由块名和hash值自动生成；
      cacheGroups:
      {
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true
        },
        app: {
          minChunks: 3,
          priority: -20,
          reuseExistingChunk: true // 复用之前打包的模块
        },
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
          chunks: "all",
          priority: -10,
        },   

      }
    },
    runtimeChunk: {
      name: entrypoint => `runtime~${entrypoint.name}`
    },
  },
})
if (buildconfig.build.productionGzip) {
  const CompressionWebpackPlugin = require('compression-webpack-plugin')

  webpackConfig.plugins.push(
    new CompressionWebpackPlugin({
      asset: '[path].gz[query]',
      algorithm: 'gzip',
      test: new RegExp(
        '\\.(' +
        buildconfig.build.productionGzipExtensions.join('|') +
        ')$'
      ),
      threshold: 10240,
      minRatio: 0.8
    })
  )
}

if (buildconfig.build.bundleAnalyzerReport) {
  const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
  webpackConfig.plugins.push(new BundleAnalyzerPlugin())
}

module.exports = webpackConfig
