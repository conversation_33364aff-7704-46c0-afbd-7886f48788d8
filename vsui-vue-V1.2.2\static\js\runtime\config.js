
    /**
 *  @author:cuiliang 
 *  @email: <EMAIL>
 *  @date：20210527
 *  @version:V1.2.0
 *  @description: 配置项，以下配置项可以在编译后、部署前进行调整
 *  
 * 
 */



window.__vsui_runtime_config__ = {

    /**
     * 前端程序发布路径，该配置项仅在生产模式（build）启作用
     * example:"/vsui/vue/" 
     * default:"/"
     * 此配置项是为了配合在容器环境的虚拟路径下发布程序使用，如不想把程序放入容器根路径下，则需要配置此项为需要的目录
     * 如想把程序发布到"http://ip:port/vsui/"下("vsui"为创建的虚拟路径或站点下vsui目录)
     * 配置了此项后，项目资源路径会指向该目录下（dist目录下的结构不变），同时在生产环境下在nginx的根目录创建此目录并作目录映射,
     * */
    __app_public_path__ : "/",

    __app_project_name__ :'清廉工程院',

    __app_page_title__ : "清廉工程院",

    __app_page_keyWords__ : "请输入您想设置的应用关键字",
    
    __app_page_description__ : "请输入您想设置的应用说明",
    

    /**
     * RestApi后端接口基地址,该配置项在开发与生产模式下均有效
     * 该基地址用于配置代理使用，因此必须是当前应用地址的子路径,末尾不加'/'
     * example1:"/restful/api" 
     * example2:"http://localhost:8080/restful/api"
     * 
     * 
     */
    __rest_base_path__ : "/restful/api",
    
    
    /**
     * 该项用于配置鉴权组件,该配置项在开发与生产模式下均有效
     * 鉴权组件的使用请访问http://***********:7002/ 
     * 搜索关键字"@vsui/lib-vueauth4vseaf"获取诸多鉴权模式的帮助支持
     */
    __vs_auth_config__ :
    {
        //vseaf服务端基地址
        whereVSEAF:"/vseaf-service",
        //在DEV模式下，用户信息来源为目录：/apidata/auth/login
        isDev:true,
        //调试模式，此模式会在框架开发模式下输出日志信息便于调试鉴权过程
        DEBUG:false
    },
    
    __page_hid_topbottom__ : false,
    
    __page_hid_sidebar__ : false,

    /*******************************************************自定义配置项请添加到下方****************************************************************/


    /***************************************配置项均在此配置项之上，配置文件结束标记，以下配置项请勿删除***********************************************/
	______finished______:""
}

