
/********************************** 以下是综合部页面优化定义的样式 *********************************************/
.background-video{
  left: 50%;
  min-height: 110%;
  min-width: 100%;
  position: fixed;
  top: 50%;
  transform: translate(-50%,-50%);
  width: 100%;
  background-size:100% 100%;
}

.zhyy-list-container{
    /* 普通列表页面整体背景 */
    /* background-color:#f2f2f2; */
    background-color:#f4f5f9;
    height: 100%;
  }
  .zhyy-list-main{
    /* 普通列表页主区域 */
    padding:8px 12px;
    background-color: #f4f5f9;
  }
  
  .zhyy-list-searchArea{
    /* 列表页面检索区域 */
    padding:13px;
    background-color:#ffffff;
    margin-bottom:8px;
    border:1px solid #EBEEF5;
    border-radius: 5px;
    box-shadow: 1px 1px 4px #d7d7d7 ;
  }
  .zhyy-list-searchAreaComplex{
    /* 列表页面检索区域-用于有淘宝查询区域的 */
    padding:16px;
    background-color:#ffffff;
    margin-bottom:2px
  }
  .zhyy-list-rightTitle {
    /* 左侧树右侧列表型-右侧标题 */
    background-color: #2384B7;
    height: 32px;
    line-height: 32px;
    padding-left: 10px;
    color: #ffffff;
    text-align: left;
    
  }
  .zhyy-list-tableArea{
    /* 列表页面数据区域 */
    background-color:#ffffff;
    box-shadow: 1px 1px 4px #b3b3b3;
  }
  .zhyy-list-searchAreaPop{
    /* 列表页面检索区域-用于弹出列表的 */
    margin-bottom:3px;
    background-color:#ffffff;
    padding: 10px;
    
  }

  *>>>.el-table--scrollable-x .el-table__body-wrapper {
      overflow-x: auto;
      overflow-y: auto;
  }



  .zhyy-tree-padding{
    padding:10px;
  }


  .zhyy_search_div{
      display: inline-block;
      width: 280px;
      padding-top:3px;
  }

  .zhyy_search_div>.el-input,
  .zhyy_search_div>.el-select,
  .zhyy_search_div>.vue-treeselect{
      width: calc(100% - 130px) !important;
  }

  .zhyy_search_div label{
      display: inline-block;
      width:100px;
      text-align: right;
  }
  

  /***************  弹出开始  ********************/
  .el-dialog__header {
    /* 弹出窗口标题区域样式 */
    border-bottom:1px solid #EAEEF5;
    text-align: left;
  }
  
  .el-dialog {
    /* 弹出窗口整体最大高度 */
    max-height:calc(100vh - 170px);
    overflow: auto;
  }
  .el-dialog__body {
    /* 弹出窗口内容区域最大高度 */
    max-height:calc(100vh - 288px);
    overflow: auto; 
    padding: 10px 10px !important;

  }

  .zhyy-pop-search-div{
    border: 1px solid #EBEEF5;
  }

  .footer{
    text-align: center;
    position: absolute;
    bottom: 0px;
    background-color: #ffffff;
    width: calc(100% - 50px);
    margin:0px auto;
  }

/***************  弹出结束  ********************/


/***************  表格开始  ********************/

/* 
  .el-table th.el-table__cell,.el-table th{
    background-color: rgb(227 240 253) !important;
  } */

  .el-table .cell{
    padding: 5px 2px 5px 2px;
  }
/* 
  .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf{
    border-bottom:1px solid #e4e5e6 !important;
    
    border-right: 1px solid #e4e5e6 !important;
  }
  */

  
/***************  表格开始  ********************/

/***************  form  ********************/

    
  .zhyy-edit-container{
    /* 普通编辑页面整体背景 */
    background-color:#fcfcfc;

  }
  .zhyy-edit-formArea{
    /* 卡片页面内容区域 */
   /*  height:calc(100vh - 188px); */
   height: auto;
    overflow:auto;
    padding-bottom: 40px;

  }
  fieldset{
    border-radius: 5px;
    border:1px solid #cccccc;
    margin-bottom: 25px;
    padding: 20px;
    background-color: #ffffff;
  }
  legend{
    font-size: 16px;
    padding: 5px;
  }

  .el-form .el-input--small .el-input__inner,.el-form .el-range-editor--small .el-input__inner{
    height:42px !important;
    line-height: 42px !important;
    color: #606266 !important;
  }


  .el-range-editor.is-disabled input
  ,.el-range-editor.is-disabled .el-range-separator
  ,.el-date-editor .el-range__icon
  ,.el-textarea.is-disabled .el-textarea__inner{
    color: #606266 !important;
  }

  .form_view_span{
    display: inline-block;
    width: calc(100% - 10px);
    background-color: #F5F5F5;
    border-radius: 8px;
    padding:5px;
    min-height: 36px;
  }
/*********  form  ********************/



  /********************** 淘宝查询区域相关样式 开始 *******************/
  .searchAdvance{
    padding-bottom:6px
  }
  .searchAdvance >>> .m-nav .group .head {
    top: -9px;
  }
  .searchKey {
    width: 300px;
  }
  .searchKey >>> .el-input__inner {
    top: 3px ;
  }
  
  .searchKey >>> .el-input__suffix {
    top: 10px;
  }
  
  .advancedBtn {
    height: 32px;
    line-height: 28px;
    display: inline-block;
    white-space: nowrap;
    cursor: pointer;
    background: #ffffff;
    border: 1px solid #dcdfe6;
    color: #606266;
    -webkit-appearance: none;
    text-align: center;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-transition: 0.1s;
    -o-transition: 0.1s;
    transition: 0.1s;
    font-weight: 500;
    padding: 0px 16px;
    font-size: 12px;
  }
  .advancedBtn .el-icon-d-arrow-left {
    transform: rotate(-90deg);
    animation: arrowRotate1 0.15s cubic-bezier(0.4, 0, 0.2, 1) 0s 1 forwards;
  }
  .advancedBtn:hover {
    color: #39f;
    border-color: #39f;
  }
  .advancedBtn.open {
    height: 32px;
    background: #f3f5f5;
    border: 1px solid #dbdbdb;
    color: #39f;
  }
  .advancedBtn.open .el-icon-d-arrow-left {
    transform: rotate(90deg);
    animation: arrowRotate2 0.15s cubic-bezier(0.4, 0, 0.2, 1) 0s 1 forwards;
  }


  .tableHiddenSpan{
    overflow:hidden;
    white-space:nowrap;
    /*文字超出宽度则显示ellipsis省略号*/
    text-overflow:ellipsis;
    
  }

  /****************** 淘宝查询区域相关样式 结束 ********************/
  .zhyy-list-title{
    font-size: 16px;
    font-weight: bold;
    border-left: 3px solid #3572bf;
    padding: 0 10px;
    position: relative;
    left: -15px;
    padding-left: 20px；
  }

  .zhyy-list-searchArea label{
    width: 100px;
    display: inline-block;
    text-align: right
  }

  /* .el-table__body-wrapper {
    overflow: hidden !important;
  }
  .el-table__body-wrapper:hover{
    overflow: auto !important;
  } */
  
  /********************************** 以上是综合部页面优化定义的样式 *********************************************/
  
  