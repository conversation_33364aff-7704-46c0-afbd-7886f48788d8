@import url("home.css");

/********************

本皮肤由崔良创建于20200416，

白黑 半透明色：
黑为主色调
白，半透明为辅色调


白色文字：#ffffff






************************/


/********************************************以下为默认皮肤配色********************************************/
html,
body,
.app,
.wrapper {
  background-color: #34225D;
  background-image: url("../../../img/themes/night1/bg.jpg") ;
  background-repeat:no-repeat;
  background-position: center;
  background-size: 100% 100%;
  transition: all 0.3s ease-in-out;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
    background-color: #373d41;

    /*width:4px;
    height:4px;*/
  }
  
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px #373737;


    background: #f6f6f6;

  }
::-webkit-scrollbar-track-piece
  {}
  /*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 6px #373737;
    background-color: #666;

    
  }
  ::-webkit-scrollbar-thumb:hover {
    -webkit-box-shadow: inset 0 0 6px #373a41;
    background-color: #777;

  }
  ::-webkit-scrollbar-corner{
    background: #999;
  }
  
  ::-webkit-scrollbar-button:horizontal:decrement {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #666;

  }
  ::-webkit-scrollbar-button:vertical:decrement {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #666;
  }
  ::-webkit-scrollbar-button:horizontal:decrement:hover {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #999;
  }
  ::-webkit-scrollbar-button:vertical:decrement:hover {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #999;
  }
  .app {
    transition: all 0.3s ease-in-out;
    background-color:transparent;
    opacity:1;
  
  }
  .app.changingtheme {
    transition: all 0.3s ease-in-out;
    opacity:0;
  }

 