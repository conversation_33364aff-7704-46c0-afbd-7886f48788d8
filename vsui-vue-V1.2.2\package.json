{"name": "VSUI.VUE", "version": "1.2.2", "description": "抽离配置项，并可在编译后/运行态调整，使用支持IE的依赖组件", "author": "<EMAIL>", "private": true, "license": "", "repository": "http://***********/UE/vsui.vue/tree/V1.2.2", "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "demo": "webpack-dev-server --inline --progress --config build/webpack.examdev.conf.js", "build": "node build/build.js ", "build:demo": "node build/buildExam.js", "start": "node dist/server.js", "build:dll": "webpack --config build/webpack.dll.conf.js"}, "dependencies": {"@babel/runtime": "^7.27.6", "@vsui/lib-jsext": "^0.1.1", "@vsui/lib-vue-webpack-devdependencies": "1.2.2", "@vsui/lib-vueauth4vseaf": "4.4.2-upgrade1", "@vsui/vue-example": "file:examples", "vue-video-player": "^5.0.1"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}}