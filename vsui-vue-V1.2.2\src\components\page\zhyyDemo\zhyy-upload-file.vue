<template>
  <div class="upload-file">
    <el-upload
      class="upload-demo"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeAvatarUpload"
      :on-preview="handleClickFileDetail"
      :before-remove="beforeRemove"
      multiple
      :http-request="myUpload"
      :action="uploadUrl"
      :limit="limit"
      :file-list="fileList"
      :disabled="isView"

    >
      <el-button size="small" type="primary" v-if="!isView" >{{ btnMsg }}</el-button>
      <div slot="tip" class="el-upload__tip" >
        <!-- 只能上传jpg/png文件，且不超过500kb -->
        {{ tipMsg }}
      </div>
    </el-upload>
    <!-- <el-upload ref="upload" class="uploadfile" :http-request="httpRequest" action="" :show-file-list="false" :on-change="handleChange" :on-exceed="handleExceed" :on-success="handleSuccess" :on-error="handleError"
                        :auto-upload="false">
                        <el-button class="upload-btn btn" type="primary" placeholder="请上传文件">选择文件</el-button>
                    </el-upload> -->
  </div>
</template>
<script>

export default {
  props: {
    limit: {
      //文件个数
      type: Number,
      default: 10,
    },
    maxSize: {
      //文件大小最大值
      type: Number,
      default: 1,
    },
    btnMsg: {
      type: String,
      default: "点击上传",
    },
    tipMsg: {
      type: String,
      default: "",
    },

    ywid: {
      type: String,
      default: "",
    },
    fjlx: {
      type: String,
      default: "",
    },
    xtmk: {
      type: String,
      default: "",
    },

    type:{
      //附件 file/图片 img
      type:String,
    },

    //是否查看模式，查看模式隐藏删除、添加功能
    isView: {
        type: Boolean,
        default: false
    }

  },
  data() {
    return {
      fileList: [],
      uploadType: '',
      ossBasePath: '',


    };
  },

  mounted() {

    var rows = [];
    if(this.ywid == '1'){
      rows = [{FJMC:'测试附件1.txt', FJXXBS:'1'}, {FJMC:'测试附件2.jpg', FJXXBS:'2'}];
    }
    rows.forEach((item) => {
        item.name = item.FJMC;
        item.id = item.FJXXBS;
        
    });
    this.fileList = rows;


    //this.getFileList();

  },
  methods: {

    getFileList(){
      let params = {
        ywid: this.ywid,
        fjlx: this.fjlx
      };
      console.log(this.ywid, 'this.ywid');
      fAPI.queryFileList(params).then(res=>{
        console.log(JSON.stringify(res.rows), 'getFileList.rows' + this.fjlx);
        if(null != res.rows){
          var rows = res.rows;
          rows.forEach((item) => {
              item.name = item.FJMC;
              item.id = item.FJXXBS;
              
          });
          this.fileList = rows;

          this.uploadType = res.uploadType;
          this.ossBasePath = res.ossBasePath;


        }
        
        
      });


    },
    //上传接口
    myUpload(param) {
      
      let fileObj = param.file;
      let formData = new FormData();
      // formData.append("inputFile", fileObj);
      if(this.type=='file'){
        formData.append("file", fileObj);
      }
      if(this.type=='img'){
        formData.append("multifileImg", fileObj);
      }

      formData.set("ywid", this.ywid);
      formData.set("fjlx", this.fjlx);
      formData.set("xtmk", this.xtmk);

      fAPI.saveMultipartFile(formData).then(res=>{
        if(res.errorMsg){
            this.$message.warning("上传失败！返回消息" + res.errorMsg);
        }else{
            this.$message.success('上传成功！');
            this.getFileList();

        }
      });
      
    },
    // 查看文件详情
    handleClickFileDetail(item) {
      //console.log(item);
      //console.log(fAPI.getBasePath());
      if(this.uploadType == 'oss'){
        window.location.href = this.ossBasePath + "/" + item.id + "/" + item.name;

      }else{
        window.location.href = fAPI.getBasePath() + "/fileUpload/download/" + item.id;

      }
      
    },

    beforeRemove(file, fileList) {
        if (this.isShowDelAsk == false) {
            this.isShowDelAsk = true;
        } else {
            return this.$confirm(`确认删除“${file.name}”吗`, "确认删除");
        }
    },

    handleRemove(file) {
      var param = {
        fileId: file.id
      }
      fAPI.deleteFile(param).then(res=>{
        if(res.errorMsg){
            this.$message.warning("删除失败！返回消息" + res.errorMsg);
        }else{
            this.$message.success('删除成功！');
            this.getFileList();
        }
      });
      
    },
    handleSuccess(response, file, fileList) {
      console.log('handleSuccess')
      console.log(response, file, fileList);
      
    },
    // 文件超出个数限制时的钩子
    handleExceed(files, fileList) {
      if (fileList.length == this.limit) {
        this.$message.warning("最多只能上传" + this.limit + "个文件!");
      }
    },
    // 文件上传之前判断文件大小及格式
    beforeAvatarUpload(file) {
      console.log(file)
      // const isJPG = file.type === "image/jpeg";
      // const isLt2M = file.size / 1024 / 1024 < 2;

      // if (!isJPG) {
      //   this.$message.error("上传头像图片只能是 JPG 格式!");
      // }
      // if (!isLt2M) {
      //   this.$message.error("上传头像图片大小不能超过 2MB!");
      // }
      // return isJPG && isLt2M;
    },
    handleError(err, file, fileList) {
      console.log(err, file, fileList);
      this.$message.error('文件上传失败！');
    },
  },
};
</script>
<style lang="scss" scoped>

</style>