<template>

  <el-container class="el-container-scoped">
    <el-aside tag="div" class="el-aside-scoped" v-show="hiddenSidebar!=''"  style="width:auto;">
        <v-sidebar :modules="modulesTree==null?[]:modulesTree" @moduleclicked="moduleclicked"></v-sidebar>
    </el-aside>
    <el-main ref="workbench"  class="el-main-scoped" style="position:relative">
        <div class="tag_box" v-if="navTabs.length>0">
            <el-tabs v-model="currentTag"
                     class="red_tag_list"
                     :closable="navTabs.length>1"
                     @tab-remove="removeTab"
                     @tab-click="handleClick">
                <el-tab-pane :label="item.name"
                             :name="item.resId"
                             :key="ind"
                             v-for="(item,ind) in navTabs"></el-tab-pane>
            </el-tabs>
<!--              <div class="tag_item">-->
<!--                  {{currentResData.resName}}-->
<!--              </div>-->
        </div>
        <div class="main_box" :style="{
          height: `calc(100% - ${navTabs.length>0?40:0}px);`
        }">
            <router-view></router-view>
        </div>
    </el-main>
  </el-container>
</template>
<script>
  import {getResIdByResPvalue,getResPvalueByResId} from "../../lib/comFun"
import vSidebar from "./Sidebar.vue";
  import {mapState} from 'vuex';
import VSAuth from "../../vsAuth";
  import store from "@/store";
  import {mixin,runtimeCfg} from "../../assets/core/index";

export default {
components: {
  vSidebar,
},
mixins:[mixin],
  created() {

      this.currentResId = this.$route.query.resId
      if(this.$route.query._cResId){
          console.log(this.$route.query._cResId)
          this.currentResId = this.$route.query._cResId
      }

      const resData = getResIdByResPvalue(this.$route.path) || {}

      this.$EventBus.$on("openNewTab", (data) => {
         console.log(data)
          const resData1 = getResIdByResPvalue(this.$route.path) || {}
          const query = data.query || {}
          if(resData1.resId) {
              query._cResId = resData1.resId
          }
          data.name = query._name
          data.resId = data.path
          const path = data.path || ''
          if(path) {
              if(!this.navTabs.some(item => item.resId === data.resId)){
                  this.navTabs.push(data)
                  store.commit('APP/setNavTabs', this.navTabs);
              }
              this.currentTag = data.resId
              this.$router.push({
                  path,
                  query
              })
          }
      });

      this.currentTag = resData.resId || ''
      if(this.navTabs.length === 0 && resData.resId && resData.resPvalue && resData.resPvalue.indexOf('http')<0) {
          const navObj = {
              resId: resData.resId,
              name: resData.resName,
              path: resData.resPvalue
          }
          store.commit('APP/setNavTabs', [navObj]);
      } else if(this.navTabs.length === 0 && this.$route.query._cResId) {
          const navObj = {
              resId: this.$route.path,
              name: this.$route.query._name,
              path: this.$route.path,
              query: this.$route.query
          }
          this.currentTag = this.$route.path
          store.commit('APP/setNavTabs', [navObj]);
      }
  },
data() {
  return {
    VSAuth,
      currentTag: '',
      currentResId: '',
    hiddenSidebar: this.$route.query.hidsid,
  };
},
  watch: {
    '$route.query':{
        handler(query) {
            this.currentResId = query.resId
            if(query._cResId){
                this.currentResId = query._cResId
            }
        },
        deep: true
    },
    '$route':{
        handler(v) {
            if(this.$route.path == '/djdHome'){
                this.currentTag = ''
            }else {
                const o = getResIdByResPvalue(this.$route.path) || {}
                if(o.resId) {
                    this.currentTag = o.resId
                }
            }
        },
        deep: true
    },
  },
computed: {
    ...mapState({navTabs: state => state.APP.navTabs}),
    currentResData() {
        if(this.$route.path === '/djdHome'){
            const resId = this.$route.query.resId
            const o = getResPvalueByResId(resId) || {}
            return o
        } else if(this.$route.query._cResId) {
            const o = getResIdByResPvalue(this.$route.query._cResId) || {}
            console.log(o,'----------------------1')
            return o
        } else {
            const o = getResIdByResPvalue(this.$route.path) || {}
            return o
        }
    },
    firstModuleResId() {
        if(!this.$route.query._cResId && this.currentResId) {
            return this.currentResId
        }
        const treeData = this.auth.instance.getModulesTree();


        let o = getResIdByResPvalue(this.$route.path) || {}
        if(this.$route.query._cResId){
            o = getResPvalueByResId(this.$route.query._cResId)
        }
        if(o.resPid !== '0'){
          let permissions= this.auth.instance.getPermission();
          if(permissions&&permissions.resList&&permissions.resList.length>0){
            const list = permissions.resList
            o = this.getFirstResId(o.resId,list)
          }
        }
        if(o.resId){
          return o.resId
            // const arr = o.resFloorId.split(',')
            // if(arr.length>1){
            //     return arr[1]
            // }
        } else if(this.$route.path === '/djdHome' && treeData.length > 0){
            return treeData[0].resId
        }
        return ''
    },
  modulesTree : function(){
        const treeData = VSAuth.getModulesTree()
      console.log(this.firstModuleResId,'-------1111',treeData)
      if(this.firstModuleResId){
          const res = treeData.find(item => item.resId === this.firstModuleResId)
          if(res) {
              if(res.children){
                  return res.children.map(item => {
                      return {
                          ...item,
                          iconClass:item.resIcon
                      }
                  })
              }
              else{
                  this.$router.push(res.resPvalue)
              }
          }
      }
      return []
  },
  currentModuleResId : function(){return this.$route.matched[1].path},//this.$route.matched[1] 地址匹配的是路由的第二层，第一层为"/"
  currentModule : function(){
    if(!this.currentModuleResId) return null;
    for(let i=0;i<this.modulesTree.length&&this.modulesTree.length>0;i++)
    {
      if(this.modulesTree[i].resPvalue.indexOf(this.currentModuleResId+"")>-1)
      {
        return this.modulesTree[i];
      }
    }
    return null;

  },
},
  mounted() {
      // this.$EventBus.$on("tabClick", (resId) => {
      //     this.currentResId = resId
      // });
  },
methods:{
    getFirstResId(resId, list) {
      const p = list.find(item => item.resId === resId)
      if(p) {
        if(p.resPid !== '0'){
          return this.getFirstResId(p.resPid,list)
        } else {
          return p
        }
      } else {
        return {}
      }

    },
    removeTab(resId) {
        const o = this.navTabs.find(item => item.resId === resId)
        const ind = this.navTabs.indexOf(o)
        this.navTabs.splice(ind,1)
        store.commit('APP/setNavTabs', this.navTabs);
        if(ind === 0){
            this.currentTag = this.navTabs[0].resId
            this.$router.push(this.navTabs[0].path)
        } else {
            this.currentTag = this.navTabs[ind-1].resId
            this.$router.push(this.navTabs[ind-1].path)
        }

        console.log(this.navTabs)
    },
    handleClick(tab,event) {
        const ind = tab.index
        // const name = tab.name
        const o = this.navTabs[ind]
        this.$router.push({
            path: o.path,
            query: o.query
        })
    },
  /**
   * 侧边栏点击事件，本函数返回true，则自动使用路由跳转
   *
   */
  moduleclicked(routerPath)
  {
    if(routerPath&&routerPath!=""&&this.$route.path!=routerPath) {
      console.log(`路由跳转：${routerPath}`)
    }
  }
},
};
</script>