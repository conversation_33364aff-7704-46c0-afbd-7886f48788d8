import {Class} from "@vsui/lib-jsext";
import themeInfo from "../assets/configs/themeInfo"

/***
 * 修改主题
 * @param string theme  主题名称
 * @param function(theme，newtheme) succCallBack fun(新主题名称，承载新主题样式的dom元素)
 * @param function(errMsg) filedCallBack fun(错误信息)
 */
function changeTheme(theme,succCallBack,filedCallBack){   
    let app=document.getElementById("app"), head = document.getElementsByTagName("head")[0];
    Class.addClass("changingtheme",app)
    let oldtheme=document.getElementById("theme")
    
    let newtheme=document.createElement("link");
    newtheme.id="theme";
    newtheme.rel="stylesheet";
    newtheme.type="text/css";
    newtheme.href=themeInfo.themeFileUrl.format(theme.path)+"?d="+Math.random()*1000;
    
    newtheme.onload = function(){
      try{
        head.removeChild(oldtheme);
        Class.removeClass("changingtheme",app)
      }catch(e){}
      if(succCallBack) succCallBack(theme,newtheme);
    };
    newtheme.onerror = function(){
      try{
        Class.removeClass("changingtheme",app)
        head.removeChild(newtheme);
      }catch(e){}
      if(filedCallBack) filedCallBack(new Error("未找到主题文件,切换主题失败!<br/>已还原主题!"))
    };
    window.setTimeout(()=>{
      head.appendChild(newtheme); 
      },100)
    
  }

  export {changeTheme}