/**
 * Powered by vue-router ^3.1.3
 * 路由拦截器，可在路由跳转过程中进行拦截处理，
 * Created in 20200118 by cuiliang 崔良于2020年1月18日，公司18周年纪念
 *
 *
 */
import store from "../store/index";

//系统级
const routerInterceptor0={
  //使用钩子函数对路由进行权限跳转
  beforeEach:function(to, from, next){
    
    store.dispatch("APP/Loading", true);
    console.log("router-before:1")
    next();
  },
  afterEach:function(transition) {
    store.dispatch("APP/Loading", false);
    console.log("router-after:1")
  },
}

//应用级
const routerInterceptor2={
  //使用钩子函数对路由进行权限跳转
  beforeEach:function(to, from, next)
  {
    console.log("router-before:3")
    next();
    
  },
  afterEach:function(transition) {
    console.log("router-after:3")
  },
}



export {routerInterceptor0,routerInterceptor2};