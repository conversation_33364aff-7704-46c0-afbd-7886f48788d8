<template>
    <div>
        <div>
            <iframe src="/static/header.html" ref="iframe" width="100%" scrolling="no" height="210px" style="border: none;"></iframe>
        </div>
        <div style="height: calc(100vh - 310px);overflow-y: auto;">
            <el-row style="text-align: center; padding: 10px 0 20px; font-size: 16px;">
                {{ itemData.infoTitle }}
            </el-row>
            <div class="video-container">
                <video-player :options="playerOptions" @play="onPlayerPlay($event)"
                    @pause="onPlayerPause($event)"></video-player>
            </div>
        </div>
        <div style="width: 1200px; margin: auto;">
            <iframe src="/static/footer.html" ref="iframe" width="100%"  scrolling="no" style="border: none;"></iframe>
        </div>
    </div>
</template>
<script>


export default {
    data() {
        return {
            itemData: {},
            playerOptions: {
                playbackRates: [0.7, 1.0, 1.5, 2.0], // 播放速度
                autoplay: false, // 如果true,浏览器准备好时开始回放。
                muted: false, // 默认情况下将会消除任何音频。
                loop: false, // 导致视频一结束就重新开始。
                preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
                language: 'zh-CN',
                aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
                fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
                sources: [{
                    type: 'video/mp4', // 类型
                    src: '' // url地址
                }],
                poster: '', // 封面地址
                notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
            },
        }
    },
    mounted() {
        const data = localStorage.getItem('itemData');
        if (data) {
            this.itemData = JSON.parse(data);
        }
        this.playerOptions.sources = [{ // 重置sources数组
            type: 'video/mp4',
            src: `/backend/vscomponent/fileupload/show?id=${this.itemData.currentVideoId}`,
        }];
        this.playerOptions.poster = `/backend/vscomponent/fileupload/show?id=${this.itemData.id}`;
    }
}
</script>
<style scoped>
.video-container {
    width: 1000px;
    margin: auto;
}

.video-player {
    width: 100% !important;
    /* height: 450px !important; */
}
</style>
