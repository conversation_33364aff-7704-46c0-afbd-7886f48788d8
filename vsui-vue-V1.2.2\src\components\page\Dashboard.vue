<template>
  <el-card shadow="never" class="el-card-scoped">
        <el-container>
          <el-main class="el-main-scoped">
            <div class="dashboard-warpper-scoped">
              <span style="font-size:16px;">帷幔已经拉开</span><br/><br/>
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              <span style="font-size:26px;">少年！</span>
              <span style="font-size:20px;">请开始你的</span>
              <span style="font-size:26px;">表演...</span>
            </div>
            <div title='我是"src/components/page/Dashboard.vue"文件' class="dashboard-img">
            </div>
      </el-main>
    </el-container>
  </el-card>
</template>
<script>

export default {
  name: "dashboard",
  data() {
    return {
      title:this.$route.meta.title,
    };
  },
  components: {
    
  },
  methods: {

  },
  mounted() {
    
  },
  computed: {
    
  },
  beforeCreate(){
    
  },
  created() {
    
  }
};
</script>

