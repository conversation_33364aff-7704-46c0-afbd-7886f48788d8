
/**********************左侧菜单*****************************/

.app .wrapper .header-box > .left-navs > .lefttop-navs  {
    overflow-y: hidden;
    overflow-x: hidden;
    width: 100%;
  }
  
  .app .wrapper .header-box > .left-navs > .lefttop-navs > .el-menu-scoped {
    box-sizing: border-box;
    -webkit-box-sizing:border-box;
  }
  .app .wrapper .header-box > .left-navs > .lefttop-navs > .el-menu-scoped  .el-submenu__title {
     padding: 0 0 !important;
     padding-left: 10px !important; 
  }
  .app .wrapper .header-box > .left-navs > .lefttop-navs > .el-menu-scoped{
    width: 100%; /*菜单展开宽度*/
  }
  
  
  
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped,
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped[class~=is-opened]{
    /*padding-left:0!important;
    padding-right:0!important;*/
    }
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped:hover
  {
  }
  /*********左侧菜单图标字体设置*************/
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped > i{
    font-size: 16px;
    font-weight: 700;
    width: 20px;
    text-align: center;
    vertical-align: middle;
  }
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped:hover .el-submenu-item-title-scoped{
  }
  
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped[class~=is-active]{
    font-weight: bold;
  }
  
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped .el-submenu__title{
  }
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped .el-submenu__title i{
  }
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover{ 
  }
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover i {
  }
  .app .wrapper .header-box > .left-navs  .lefttop-navs > .el-menu-scoped .el-menu-item-scoped .el-submenu-item-title-scoped{
    margin-left: 0px!important;
    visibility: visible !important;
    height:unset ;
    width: unset ;
  }
  .app .wrapper .header-box > .left-navs  .lefttop-navs  .el-menu-scoped .el-menu-item-scoped .el-tree-scoped {
  }
  
  .app .wrapper .header-box > .left-navs  .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node{
    padding-left: 15px;
  }
  .app .wrapper .header-box > .left-navs  .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content
  {
    padding-left: 0px !important;
    height: 50px;
    width: 100%;
  
  }
  .app .wrapper .header-box > .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content>.el-tree-node__expand-icon
  {
  padding:unset;
  }
  .app .wrapper .header-box > .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content:hover{
  }
  
  
  .app .wrapper .header-box > .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content:hover{
  }
  
  
  .app .wrapper .header-box > .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node[class*="is-current"] > .el-tree-node__content {
    font-weight: bold;
  }
  