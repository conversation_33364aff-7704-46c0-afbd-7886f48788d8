<template>
    <div class="wrapper">
        <vs-head></vs-head>
        <canvas class="constellation" width="1920" height="887"></canvas>
        <div class="ms-login">
            <div class="ms-title">{{runtimeCfg.app_project_name}}</div>
            <el-form label-width="0px" class="loginForm">
                <el-form-item prop="username">
                    <el-input v-model="username" placeholder="请输入用户名" clearable></el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input v-model="password" placeholder="请输入密码" clearable @keyup.enter.native="login" type="password"></el-input>
                </el-form-item>
                <div class="login-btn">
                    <el-button type="primary" @click="login">登录</el-button>
                </div>
            </el-form>
        </div>
        <vs-bottom></vs-bottom>
    </div>
</template>

<script>
import vsHead from "../components/common/loginHeader.vue";
import vsBottom from "../components/common/loginBottom.vue";
import {auth,runtimeCfg} from "../assets/core/index";
import {Cookie} from "@vsui/lib-jsext";
import Constellation from "../lib/constellation.js";

export default {
  components: {
    vsHead,
    vsBottom
  },
  data: function() {
    return {
      runtimeCfg,
      username: "admin",
      password: "123456"
    };
  },
  mounted() {
    this.clearTheme();
    this.initConstellation();
  },
  methods: {
    initConstellation(){
      if(!document.querySelector('.constellation')){
        return;
      }
        let con=new Constellation(document.querySelector('.constellation'), {
            star: {
                color: 'rgba(255, 255, 255, .8)',
                width: 1.5,
                randomWidth: true
            },
            line: {
                color: 'rgba(255, 255, 255, .8)',
                width: 0.2
            },
            position: {
                x: 0, // This value will be overwritten at startup
                y: 0 // This value will be overwritten at startup
            },
            width: window.innerWidth,
            height: window.innerHeight,
            velocity: 0.3,
            length: 80,
            distance: (window.innerWidth < 1000) ? 40 : 100,
            radius: 250,
            stars: []
        })
        con.init();
      
    },
    login() {
      const _this = this;
      console.log("this.username:" + this.username);
      if (this.username == "") {
        this.$message({
          message: "请输入用户名！",
          type: "error"
        });
        return;
      }
      if (this.password == "") {
        this.$message({
          message: "请输入密码",
          type: "error"
        });
        return;
      }
      auth.instance.login(
        this.username,
        this.password).then(()=>{ 
          _this.$router.push("/");
        }).catch(error=>{
          _this.$message({
              message: `登录过程出错，失败原因是：${error.message}`,
              dangerouslyUseHTMLString: true,
              type: "error"
            });
        });
    },
    clearTheme(){
      (document.getElementById("theme")).href="";
    }
  }
};
</script>

<style scoped>

.wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url(../../static/img/loginbg.png);
  background-repeat: no-repeat;
  background-size:100% 100%;
}
.wrapper .ms-login {
  position: absolute;
  right: 10%;
  top: 30%;
  width: 380px;
  height: 360px;
  margin: -50px 0 0 -200px;
  border-radius: 5px;
  background: #fff;
  opacity: 0.8;
}
.wrapper .ms-login .ms-title {
  width: 90%;
  position: relative;
  margin: 25px auto 30px auto;
  text-align: left;
  font-size: 20px;
  color: #000;
}
.wrapper .ms-login .loginForm {
  width: 90%;
  margin: 20px auto 20px auto;
}

.wrapper .ms-login .loginForm .login-btn button {
  margin-bottom: 15px;
  width: 100%;
  height: 36px;
  text-align: center;
}

.wrapper .ms-login .loginForm .forgotPwd {
  float: right;
}
.wrapper .constellation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    }
</style>