
/**********************左侧菜单*****************************/

.app .wrapper .header-box .left-navs .lefttop-navs  {
    background-color: #ffffff;
  }
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped {
    background-color: #ffffff;
    border-right: 0px solid #dddddd;
    color: #333333;
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-submenu__title {
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped{
}
  
  
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped,
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped[class~=is-opened]{
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped:hover
{
    background-color: #eeeeee;
    color: #111111;
}
/*********左侧菜单图标字体设置*************/
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped i{
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped:hover .el-submenu-item-title-scoped{
    color: #111111; 
}
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active]{
    background-color: #F2F2F2;
    color: #000000;
}
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title{
    background: transparent;
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title i{
    color: #333333;
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover{
    color: #111111;
    background: transparent;
 }
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover i {
    color: #111111;
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-submenu-item-title-scoped{
    color: #333333; 
}
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped {
    background: #ffffff;
}
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content
{
    color:#333333;
    background: transparent; 
}

.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content:hover{
    color: #111111 !important;
    background-color: #eeeeee !important;
}
  
.app .wrapper .header-box .left-navs .lefttop-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node[class*="is-current"] > .el-tree-node__content {
    color: #000000 ;
}
  
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped i{
}
  
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped .custom-tree-node {
}