<template>
  <div class="zhyy-edit-container">
    <div class="zhyy-edit-formArea" >
      <el-form ref="dataForm" :model="formData" :rules="rules" style="padding:16px; height: auto;">
        <el-row>
          <el-col :span="24">
            <el-form-item label="审核人名称" prop="SHRMC" label-width="120px" >
              <el-input v-model="formData.SHRMC" readonly placeholder="点击按钮选择审核人" :disabled="isView">
                <el-button v-if="!isView" slot="append" type="primary" @click="popListSelect" style="color: white;">选择审核人</el-button>
              </el-input> 
            </el-form-item>
            <el-form-item label="审核人账号" prop="SHRZH" label-width="120px" >
              <el-input v-model="formData.SHRZH" placeholder="" :disabled="isView">
              </el-input> 
            </el-form-item>
            <el-form-item label="审核人单位" prop="SHDWMC" label-width="120px" >
              <el-input v-model="formData.SHDWMC" placeholder="" :disabled="isView">
              </el-input> 
            </el-form-item>
          </el-col>
        </el-row>

        <div class="button_div" v-if="!isView">
          <el-form-item size="large" align="center">
            <el-button type="success" @click="saveData">保存</el-button>
            <el-button type="info" @click="closeDialog">关闭</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 以下是简单弹出列表页 -->
    <el-dialog  v-dialogDrag 
      v-if="popListFormVisable"
      title="人员选择列表" 
      width="980px"
      :visible.sync="popListFormVisable" 
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :destroy-on-close="true"
      @close="handleDialogClose">
        <shrxxEdit @confirmSelect="handlePopListSelect"></shrxxEdit>
    </el-dialog>
  </div>
</template>

<script>
import shrxxEdit from "./shrxxEdit";
import { v4 as getUUID } from 'uuid'
import VSAuth from "@vsui/lib-vueauth4vseaf";

export default {
  name: "shwhEdit",
  components: {
    shrxxEdit
  },
   
  data() {
    return {
      user:VSAuth.getAuthInfo().permission,
      isView: false,  // 添加查看模式标志
      uuid: getUUID().replace(/-/g, '').toUpperCase(),

      formData: {
        PZID: undefined,
        SHRID: undefined,
        SHRMC: undefined,
        SHRZH: undefined,
        SHDWID: undefined,
        SHDWMC: undefined,
        CJRID: undefined,
        CJRMC: undefined,
        CJSJ: undefined,
      },

      rules: {
        SHRMC: [{
          required: true,
          message: '请选择审核人，审核人不得为空',
          trigger: 'blur'
        }],
      },

      popListFormVisable: false,
    }
  },
  props: {
    operation: {
      type: String,
      default: ""
    },
    shwhEditData: {
      type: Object,
      default: {}
    },
  },
  mounted() {
    console.log('路由参数：', this.operation, this.shwhEditData);
    if (this.operation === 'add') {
      console.log('新建操作');
      this.isView = false;
      this.formData.PZID = this.uuid;
      this.formData.CJRID = VSAuth.getAuthInfo().permission.userId;
      this.formData.CJRMC = VSAuth.getAuthInfo().permission.userName;
      this.formData.CJSJ = new Date().format("yyyy-MM-dd hh:mm:ss");
    } else if (this.operation === 'edit' || this.operation === 'view') {
      this.isView = this.operation === 'view';  // 设置查看模式
      this.formData.PZID = this.shwhEditData.PZID
      this.formData.SHRID = this.shwhEditData.SHRID
      this.formData.SHRMC = this.shwhEditData.SHRMC
      this.formData.SHRZH = this.shwhEditData.SHRZH
      this.formData.SHDWID = this.shwhEditData.SHDWID
      this.formData.SHDWMC = this.shwhEditData.SHDWMC
    }
  },
  methods: {
    closeDialog() {
      this.$emit('handleSubmitSuccess');
    },

    async saveData() {
      var isValid = false;
      await this.$refs.dataForm.validate(valid => {
        isValid = valid;
      });
      if(!isValid) {
        this.$alert('请选择审核人，审核人不得为空！', '提示', {
          type: 'error'
        });
        return;
      }
      this.axios({
        url: "/backend/shwhController/insertShwhList",
        method: "post",
        data: {
          ...this.formData
        },
      })
      .then((resp) => {
        this.$notify({
          title: "提示",
          message: "保存成功",
          type: "success",
          duration: 2000,
          offset: 80,
        });
        // 添加成功后返回调用钩子函数，关闭弹窗 
        this.$emit('handleSubmitSuccess');
      })
      .catch((error) => {
        this.$message.error(error);
      });
    },

    /**
     * @description: 打开简单的弹出选择页
     * @param {*}
     * @return {*}
     */
    popListSelect() {
      this.popListFormVisable = true;
    },

    /**
     * @description: 确认
     * @param {*}
     * @return {*}
     */
    handlePopListSelect(rowData) {
      console.log(rowData);
      console.log("*******************************", rowData)
      console.log("2222222222222222222222222222222", rowData.USERID)
      this.formData.SHRID = rowData.USERID;
      this.formData.SHRMC = rowData.USERNAME;
      this.formData.SHRZH = rowData.USERLOGINNAME;
      this.formData.SHDWID = rowData.ORGNAID;
      this.formData.SHDWMC = rowData.ORGNANAME;

      this.popListFormVisable = false;
      // 清除表单验证
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate('SHRMC');
      });
    },

    handleDialogClose() {
      this.popListFormVisable = false;
    },
  },
}

</script>

<style scoped>

</style>