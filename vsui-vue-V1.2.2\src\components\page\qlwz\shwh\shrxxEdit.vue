<template>
  <div class="zhyy-list-container">
    <div class="zhyy-pop-search-div">
      <el-row class="zhyy-list-searchAreaPop" >
        <el-col :span="24">
          <label>姓名：</label>
          <el-input @keyup.enter.native="processSearch" style="width: 200px;" 
            class="filter-item" placeholder="人员名称" v-model="listQuery.USER_NAME" >
          </el-input>
          <el-button type="primary" icon="el-icon-search" style="margin-left:8px;" @click="processSearch">查询</el-button>
        </el-col>
      </el-row>
    </div>
      
    <el-row class="zhyy-list-tableArea" >
      <el-table 
        :stripe="true"  
        v-loading="listLoading" 
        highlight-current-row
        ref="listTable"
        :data="tableData" 
        :row-key="getRowKey"
        :header-cell-style="{ background: '#F4F7FA'}">
          <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px"></el-table-column>
          <el-table-column min-width="10%" align="left" header-align="center" label="称名">
            <template slot-scope="scope">
              <span>{{scope.row.USER_NAME}}</span>
            </template>
          </el-table-column>
          <el-table-column min-width="10%" align="center" header-align="center" label="账号">
            <template slot-scope="scope">
              <span>{{scope.row.USER_LOGINNAME}}</span>
            </template>
          </el-table-column>
          <el-table-column min-width="25%" align="center" header-align="center" label="单位">
            <template slot-scope="scope">
              <span>{{scope.row.ORGNA_NAME}}</span>
            </template>
          </el-table-column>
          <el-table-column min-width="10%" align="center" header-align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="success" @click="confirmSelect(scope.row)">选择</el-button>
            </template>
          </el-table-column>
      </el-table>
      <div class="zhyy-list-paginationArea">
        <el-pagination background 
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" 
          :page-sizes="[10, 20, 30, 50]" 
          :page-size="listQuery.pageSize"
          :current-page="listQuery.pageIndex"
          layout="total, sizes, prev, pager, next, jumper" 
          :total="total">
        </el-pagination>
      </div>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "shrxxEdit",
  components: {
  },
  data() {
    return {
      total: null,
      listLoading: true,
      tableData: undefined,
      pkColumn: 'PZID',
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        USER_NAME: "",
      },
    };
  },
  mounted() {
    this.loadListData();
  },
  methods: {
    /**
     * @description: 加载列表数据
     * @param {*}
     * @return {*}
     */
    loadListData() {
      this.listLoading = false;
      this.axios({
        url: "/backend/shwhController/selectUserSelectInfo",
        method: "post",
        data: {
          ...this.listQuery
        },
      })
      .then((resp) => {
        this.tableData = resp.data.data.rows;
        this.total = resp.data.data.total
      })
      .catch((error) => {
        this.$message.error(error);
      });
    },

    /**
     * @description: 查询数据，需要把页数重置为1
     * @param {*}
     * @return {*}
     */
    processSearch() {
      this.listQuery.pageIndex = 1;
      this.loadListData();
    },

    /**
     * @description: 变更每页条数时自动加载数据
     * @param {*}
     * @return {*}
     */
    handleSizeChange(val) {
      this.listQuery.pageSize = val;
      this.loadListData();
    },
      
    /**
     * @description: 点击某一页时自动加载数据
     * @param {*}
     * @return {*}
     */
    handleCurrentChange(val) {
      this.listQuery.pageIndex = val;
      this.loadListData();
    },

    getRowKey(rowData){
      return rowData[this.pkColumn];
    },

    confirmSelect(currentSelect){
      this.$emit("confirmSelect", currentSelect);
    },
  }, 
};

</script>

<style scope>

</style>