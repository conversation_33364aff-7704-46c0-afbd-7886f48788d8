<template>
  <div class="zhyy-list-container">
    <div class="zhyy-pop-search-div">
      <el-row class="zhyy-list-searchAreaPop" >
        <el-col :span="24">
          <label>姓名：</label>
          <el-input @keyup.enter.native="processSearch" style="width: 200px;" 
            class="filter-item" placeholder="人员名称" v-model="listQuery.USERNAME" >
          </el-input>
          <el-button type="primary" icon="el-icon-search" style="margin-left:8px;" @click="processSearch">查询</el-button>
        </el-col>
      </el-row>
    </div>
      
    <el-row class="zhyy-list-tableArea" >
      <el-table 
        :stripe="true"  
        v-loading="listLoading" 
        highlight-current-row
        ref="listTable"
        :data="tableData" 
        :row-key="getRowKey"
        :header-cell-style="{ background: '#F4F7FA'}">
          <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px"></el-table-column>
          <el-table-column min-width="10%" align="left" header-align="center" label="称名">
            <template slot-scope="scope">
              <span>{{scope.row.USERNAME}}</span>
            </template>
          </el-table-column>
          <el-table-column min-width="10%" align="center" header-align="center" label="账号">
            <template slot-scope="scope">
              <span>{{scope.row.USERLOGINNAME}}</span>
            </template>
          </el-table-column>
          <el-table-column min-width="25%" align="center" header-align="center" label="单位">
            <template slot-scope="scope">
              <span>{{scope.row.ORGNANAME}}</span>
            </template>
          </el-table-column>
          <el-table-column min-width="10%" align="center" header-align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="success" @click="confirmSelect(scope.row)">选择</el-button>
            </template>
          </el-table-column>
      </el-table>
      <div class="zhyy-list-paginationArea">
        <el-pagination background 
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" 
          :page-sizes="[10, 20, 30, 50]" 
          :page-size="listQuery.pageSize"
          :current-page="listQuery.pageIndex"
          layout="total, sizes, prev, pager, next, jumper" 
          :total="total">
        </el-pagination>
      </div>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "shrxxEdit",
  components: {
  },
  data() {
    return {
      total: null,
      listLoading: true,
      // tableData: undefined,
      tableData: [
        {
          PZID: "001",
          USERNAME: "张三",
          USERLOGINNAME: "zhangsan001",
          ORGNANAME: "北京市财政局"
        },
        {
          PZID: "002",
          USERNAME: "李四",
          USERLOGINNAME: "lisi002",
          ORGNANAME: "上海市人事局"
        },
        {
          PZID: "003",
          USERNAME: "王五",
          USERLOGINNAME: "wangwu003",
          ORGNANAME: "广州市技术监督局"
        },
        {
          PZID: "004",
          USERNAME: "赵六",
          USERLOGINNAME: "zhaoliu004",
          ORGNANAME: "深圳市市场监管局"
        },
        {
          PZID: "005",
          USERNAME: "钱七",
          USERLOGINNAME: "qianqi005",
          ORGNANAME: "杭州市发展改革委"
        },
        {
          PZID: "006",
          USERNAME: "孙八",
          USERLOGINNAME: "sunba006",
          ORGNANAME: "南京市教育局"
        },
        {
          PZID: "007",
          USERNAME: "周九",
          USERLOGINNAME: "zhoujiu007",
          ORGNANAME: "武汉市卫生健康委"
        },
        {
          PZID: "008",
          USERNAME: "吴十",
          USERLOGINNAME: "wushi008",
          ORGNANAME: "成都市住建局"
        },
        {
          PZID: "009",
          USERNAME: "郑一一",
          USERLOGINNAME: "zhengyiyi009",
          ORGNANAME: "重庆市交通局"
        },
        {
          PZID: "010",
          USERNAME: "王二二",
          USERLOGINNAME: "wangerer010",
          ORGNANAME: "天津市环保局"
        },
        {
          PZID: "011",
          USERNAME: "李三三",
          USERLOGINNAME: "lisansan011",
          ORGNANAME: "西安市文化旅游局"
        },
        {
          PZID: "012",
          USERNAME: "张四四",
          USERLOGINNAME: "zhangsisi012",
          ORGNANAME: "青岛市农业农村局"
        },
        {
          PZID: "013",
          USERNAME: "刘五五",
          USERLOGINNAME: "liuwuwu013",
          ORGNANAME: "大连市商务局"
        },
        {
          PZID: "014",
          USERNAME: "陈六六",
          USERLOGINNAME: "chenliuliu014",
          ORGNANAME: "厦门市科技局"
        },
        {
          PZID: "015",
          USERNAME: "杨七七",
          USERLOGINNAME: "yangqiqi015",
          ORGNANAME: "宁波市工信局"
        },
        {
          PZID: "016",
          USERNAME: "黄八八",
          USERLOGINNAME: "huangbaba016",
          ORGNANAME: "苏州市民政局"
        },
        {
          PZID: "017",
          USERNAME: "林九九",
          USERLOGINNAME: "linjiujiu017",
          ORGNANAME: "无锡市司法局"
        },
        {
          PZID: "018",
          USERNAME: "徐十十",
          USERLOGINNAME: "xushishi018",
          ORGNANAME: "常州市应急管理局"
        },
        {
          PZID: "019",
          USERNAME: "朱一二",
          USERLOGINNAME: "zhuyier019",
          ORGNANAME: "南通市统计局"
        },
        {
          PZID: "020",
          USERNAME: "马二三",
          USERLOGINNAME: "maersan020",
          ORGNANAME: "扬州市审计局"
        }
      ],
      pkColumn: 'PZID',
      listQuery: {
        pageIndex: 1,
        pageSize: 10,
        USERNAME: "",
      },
    };
  },
  mounted() {
    this.loadListData();
  },
  methods: {
    /**
     * @description: 加载列表数据
     * @param {*}
     * @return {*}
     */
    loadListData() {
      this.listLoading = false;
      this.axios({
        url: "/backend/shwhController/selectUserSelectInfo",
        method: "post",
        data: {
          ...this.listQuery
        },
      })
      .then((resp) => {
        this.tableData = resp.data.data.rows;
        this.total = resp.data.data.total
      })
      .catch((error) => {
        this.$message.error(error);
      });
    },

    /**
     * @description: 查询数据，需要把页数重置为1
     * @param {*}
     * @return {*}
     */
    processSearch() {
      this.listQuery.pageIndex = 1;
      this.loadListData();
    },

    /**
     * @description: 变更每页条数时自动加载数据
     * @param {*}
     * @return {*}
     */
    handleSizeChange(val) {
      this.listQuery.pageSize = val;
      this.loadListData();
    },
      
    /**
     * @description: 点击某一页时自动加载数据
     * @param {*}
     * @return {*}
     */
    handleCurrentChange(val) {
      this.listQuery.pageIndex = val;
      this.loadListData();
    },

    getRowKey(rowData){
      return rowData[this.pkColumn];
    },

    confirmSelect(currentSelect){
      this.$emit("confirmSelect", currentSelect);
    },
  }, 
};

</script>

<style scope>

</style>