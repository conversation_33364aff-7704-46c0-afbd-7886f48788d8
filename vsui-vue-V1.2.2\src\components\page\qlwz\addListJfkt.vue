<template>
    <div class="dialog_form">
        <el-form ref="formData" :model="formData" :rules="rules" size="small" label-width="100px">

            <el-form-item label-width="80px" label="标题" prop="infoTitle">
                <el-input v-model="formData.infoTitle" placeholder="请输入标题" clearable :style="{ width: '100%' }"
                    :maxlength="100" show-word-limit :disabled="viewVisable">
                </el-input>
            </el-form-item>
            <el-form-item label-width="80px" label="发布人" prop="operatorName">
                <el-input v-model="formData.operatorName" placeholder="请输入发布人" :disabled="true"
                    :style="{ width: '100%' }" :maxlength="100" show-word-limit>
                </el-input>
            </el-form-item>
            <el-form-item label-width="80px" label="发布时间" prop="publishTime">
                <el-date-picker v-model="formData.publishTime" type="datetime" placeholder="发布时间"
                    value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" :disabled="true">
                </el-date-picker>
            </el-form-item>
            <el-form-item label-width="80px" label="主题图片" required="true">
                <vsfileupload v-if="showUpload && !viewVisable" ref="upload" :busId="fileParams.busId"
                    :ywlb="type + 'img'" accept="image/jpeg, image/png, image/gif, image/jpg">
                </vsfileupload>
                <vsfileupload class="uploadFile" v-if="viewVisable && showUpload" ref="upload" :busId="fileParams.busId"
                    :ywlb="type + 'img'" :editable="false">
                </vsfileupload>

            </el-form-item>
            <el-form-item label-width="80px" label="主题视频" required="true">

                <vsfileupload v-if="showUpload && !viewVisable" ref="upload" :busId="fileParams.busId" :ywlb="type + 'video'"
                    accept="video/mp4, video/avi">
                </vsfileupload>
                <vsfileupload class="uploadFile" v-if="viewVisable && showUpload" ref="upload" :busId="fileParams.busId"
                    :ywlb="type + 'video'" :editable="false">
                </vsfileupload>

            </el-form-item>
            <el-form-item size="large" align="center" class="button_style">
                <el-button v-if="!viewVisable" type="primary" @click="saveData(0)">保存</el-button>
                <el-button v-if="!viewVisable" type="success" @click="submitForm(1)">提交</el-button>
                <el-button @click="closeForm">关闭</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import vsfileupload from '../../common/vsfileupload.vue';
import { v4 as getUUID } from 'uuid';
import VSAuth from "@vsui/lib-vueauth4vseaf";
export default {
    components: {
        vsfileupload,
    },
    props: {
        type: {
            type: String,
            default: ""
        },
        rowData: Object,//列表传入的行数据
        operation: String,//用来区分是新增还是编辑

    },
    data() {
        return {
            fileParams: {
                busId: '',//业务ID
                ywlb: '',//附件业务类型，区分同一数据多个文件类型
            },
            allowedPictureTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'], // 允许的图片类型
            allowedVideoTypes: ['video/mp4', 'video/avi'], // 允许的视频类型
            formData: {
                infoId: '',//主键
                infoTitle: '',//标题
                infoNotes: '',//备注
                infoType: '',//类型
                processStatus: '',//审核状态
                infoStatus: '',//发布状态
                publishTime: '',//发布时间
                operatorId: '',//发布人id
                operatorName: VSAuth.getAuthInfo().permission.userName,//发布人名称
                operatororgId: '',//发布人单位id
                operatororgName: '',//发布人单位名称
            },
            rules: {
                infoTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],
                operatorName: [{ required: true, message: '请输入发布人', trigger: 'blur' }],
                publishTime: [{ required: true, message: '请选择发布时间', trigger: 'change' }],
            },
            showUpload: false,
            viewVisable: false
        }
    },
    mounted() {
        this.formData.infoType = this.type;
        if (this.operation == 'editJFKT') {
            this.formData.infoId = this.rowData.infoId;
        } else {
            this.formData.publishTime = new Date().toLocaleString();
        }
        if (this.operation == 'viewJFKT') {
            this.formData.infoId = this.rowData.infoId;
            this.viewVisable = true;
        }
        this.getInfoData();
    },
    methods: {
        //保存信息
        saveData(zt) {
            var isValid = false;
            this.$refs.formData.validate((valid) => {
                isValid = valid;
            });
            if (!isValid) {
                this.$message({ message: '页面有必填项未填写', type: 'error' });
                return;
            }
            let fileList = this.$refs.upload.fileList;
            if (!fileList || fileList.length == 0) {
                this.$notify({ title: '警告', message: '请上传主题图片和视频', type: 'warning' });
                return;
            }
            if (zt == 0) {//点击保存按钮
                this.formData.processStatus = '0';//草稿状态
                this.formData.infoStatus = '0';//未发布状态
            } else if (zt == 1) {
                //对于纪法课堂没有审核流程
                this.formData.processStatus = '3';//完成状态
                this.formData.infoStatus = '1';//发布状态
            }
            this.formData.operatorId = VSAuth.getAuthInfo().permission.userId;
            this.formData.operatorName = VSAuth.getAuthInfo().permission.userName;
            this.formData.operatororgId = VSAuth.getAuthInfo().permission.orgnaId;
            this.formData.operatororgName = VSAuth.getAuthInfo().permission.orgnaName;

            this.axios({
                method: "post",
                url: "/backend/qlwz/saveInfoData",
                data: this.formData

            }).then(response => {
                if (response.data.data.status === 'success') {
                    this.$message.success("保存成功！");
                    this.closeForm();
                } else {
                    this.$message.error("保存失败！");
                }
            }).catch(error => {
                this.$message.error("保存失败！")
            })


        },
        //提交数据
        submitForm(zt) {
            this.$confirm("确定要提交吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.saveData(zt);

            }
            ).catch(() => { });

        },
        //获取某条数据:根据infoId查询数据
        getInfoData() {
            this.axios({
                method: "post",
                url: "/backend/qlwz/queryInfoDataById",
                data: {
                    infoId: this.formData.infoId,
                },
            }).then(resp => {
                if (resp.data.data && resp.data.data.infoId) {
                    this.formData = resp.data.data;
                    if (!resp.data.data.publishTime) {
                        this.formData.publishTime = new Date().toLocaleString();
                    }
                    this.fileParams.busId = this.formData.infoId;
                } else {
                    this.formData.infoId = getUUID().replace(/-/g, '').toUpperCase();
                    this.fileParams.busId = this.formData.infoId;
                }
                this.showUpload = true;

            })
        },
        closeForm() {
            this.$emit('handleCloseViewDialog');
        },
    }


}
</script>
<style scoped>
.fupload {
    width: 125px;
    float: right;
}
.uploadFile{
    margin-top: -37px;
}
.button_style{
    margin-left: -100px;
}
</style>