
/* 蓝色主要按钮 */
.el-button--primary{
    background-color: #409eff !important;
    background-image: linear-gradient(315deg, #409eff 0%, #0870d8 74%) !important;
    border: none !important;
    z-index: 1;
    position: relative;
    cursor: url(mchoose.svg),auto;
  }

  .el-button--primary i{
    color: #fff;
    }


  .el-button--primary:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    border-radius: 5px;
    background-color: #409eff;
    background-image: linear-gradient(315deg, #0870d8 0%, #409eff 74%);
    box-shadow:
     -7px -7px 20px 0px #fff9,
     -4px -4px 5px 0px #fff9,
     7px 7px 20px 0px #0002,
     4px 4px 5px 0px #0001;
    transition: all 0.3s ease;
  }
  .el-button--primary:hover {
    color: #fff;
    cursor: url(mchoose.svg),auto;
  }
  .el-button--primary:hover:after {
    top: 0;
    height: 100%;
  }
  .el-button--primary:active {
    top: 2px;
  }
  
  

  

  
/*青色 */
.el-button--success {
    background-color: #89d8d3 !important;
    background-image: linear-gradient(315deg, #89d8d3 0%, #03c8a8 74%) !important;
    border: none !important;
    z-index: 1;
    position: relative;
    cursor: url(mchoose.svg),auto;
  }
  .el-button--success:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    border-radius: 5px;
     background-color: #4dccc6;
  background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);
    box-shadow:
     -7px -7px 20px 0px #fff9,
     -4px -4px 5px 0px #fff9,
     7px 7px 20px 0px #0002,
     4px 4px 5px 0px #0001;
    transition: all 0.3s ease;
  }
  .el-button--success:hover {
    cursor: url(mchoose.svg),auto;
    color: #fff;
  }
  .el-button--success:hover:after {
    top: 0;
    height: 100%;
  }
  .el-button--success:active {
    top: 2px;
  }
  

/* 红色 */
  
  .el-button--danger {
    background-color: #f7936c !important;
    background-image: linear-gradient(315deg, #f7936c 0%, #F56C6C 74%) !important;
    border: none !important;
    z-index: 1;
    position: relative;
    cursor: url(mchoose.svg),auto;
  }
  .el-button--danger:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    border-radius: 5px;
     background-color: #F56C6C;
  background-image: linear-gradient(315deg, #F56C6C 0%, #f7936c 74%);
    box-shadow:
     -7px -7px 20px 0px #fff9,
     -4px -4px 5px 0px #fff9,
     7px 7px 20px 0px #0002,
     4px 4px 5px 0px #0001;
    transition: all 0.3s ease;
  }
  .el-button--danger:hover {
    color: #fff;
    cursor: url(mchoose.svg),auto;
  }
  .el-button--danger:hover:after {
    top: 0;
    height: 100%;
  }
  .el-button--danger:active {
    top: 2px;
  }
  



  
/* 灰色 */
  
.el-button--info {
  background-color: #b2b3b4 !important;
  background-image: linear-gradient(315deg, #b2b3b4 0%, #909399 74%) !important;
  border: none !important;
  z-index: 1;
  position: relative;
  cursor: url(mchoose.svg),auto;
}
.el-button--info:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 5px;
  background-color: #909399;
  background-image: linear-gradient(315deg, #909399 0%, #b2b3b4 74%);
  box-shadow:
   -7px -7px 20px 0px #fff9,
   -4px -4px 5px 0px #fff9,
   7px 7px 20px 0px #0002,
   4px 4px 5px 0px #0001;
  transition: all 0.3s ease;
}
.el-button--info:hover {
  color: #fff;
  cursor: url(mchoose.svg),auto;
}
.el-button--info:hover:after {
  top: 0;
  height: 100%;
}
.el-button--info:active {
  top: 2px;
}

