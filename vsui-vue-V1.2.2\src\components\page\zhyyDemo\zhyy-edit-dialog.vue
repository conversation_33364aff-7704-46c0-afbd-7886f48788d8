<template>
    <div class="dialog_form">
        <el-form ref="dataForm" :model="formData" :rules="rules" size="small" label-width="100px" >
            <el-row type="flex" justify="start" align="top" :gutter="15">
                <el-col :span="12">
                    <el-form-item label-width="80px" label="SQL类型" prop="LB">
                        <el-select v-model="formData.LB" placeholder="请选择SQL类型" :style="{width: '100%'}">
                            <el-option v-for="(item, index) in LBOptions" :key="index" :label="item.label"
                                :value="item.value" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label-width="80px" label="表单名称" prop="NAME">
                        <el-input v-model="formData.NAME" placeholder="请输入表单名称" clearable :style="{width: '100%'}"
                            :maxlength="100" show-word-limit>
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row type="flex" justify="start" align="top" :gutter="15">
                <el-col :span="12">
                    <el-form-item label-width="80px" label="表单ID" prop="FORMID">
                        <el-input v-model="formData.FORMID" placeholder="请输入表单ID,不允许重复" @change="handlerFormIdChange"
                            auto-complete="on" clearable :style="{width: '100%'}" :maxlength="32" show-word-limit>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label-width="80px" label="CODE" prop="CODE">
                        <el-input v-model="formData.CODE" placeholder="请输入CODE" clearable
                            :style="{width: '100%'}">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label-width="80px" label="SQL" prop="SQL">
                <el-input v-model="formData.SQL" type="textarea" placeholder="请输入SQL"
                    :autosize="{minRows: 8, maxRows: 16}" :style="{width: '100%'}" :maxlength="4000" show-word-limit>
                </el-input>
            </el-form-item>
            
            <el-form-item size="large" align="center">
                <el-button type="primary" @click="saveData">保存</el-button>
                <el-button type="success" @click="submitForm">提交</el-button>
                <el-button @click="closeForm">关闭</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
export default {
    data() {
        return {
            formData: {
                LB: 'QUERY',
                NAME: undefined,
                DATASOURCE: undefined,
                ATHOR: undefined,
                CRTIME: undefined,
                FORMID: undefined,
                CODE: undefined,
                SQL: undefined,
                ARGIN: undefined,
                ARG: undefined,
                CONIN: undefined,
                CON: undefined,
                CON_IF: undefined,
                ID: undefined,
            },
            con_if_show : false,
            rules: {
                LB: [{
                    required: true,
                    message: '请选择SQL类型',
                    trigger: 'change'
                }],
                NAME: [{
                    required: true,
                    message: '请输入表单名称',
                    trigger: 'blur'
                }],
                DATASOURCE: [],
                ATHOR: [],
                CRTIME: [],
                FORMID: [{
                    required: true,
                    message: '请输入表单ID',
                    trigger: 'blur'
                }],
                CODE: [{
                    required: true,
                    message: '请输入CODE',
                    trigger: 'blur'
                }],
                SQL: [{
                    required: true,
                    message: '请输入SQL',
                    trigger: 'blur'
                }],
                ARGIN: [],
                CONIN: [],
                
            },
            LBOptions: [{
                "label": "查询",
                "value": "QUERY"
            }, {
                "label": "执行",
                "value": "EXECUTE"
            }],
        }
    },

    components: {},
    props: {
        id: {
            type: String,
            default: ""
        },
        operation: {
            type: String,
            default: ""
        },
        newId: {
            type: String,
            default: ""
        },
    },
    mounted() {
        let {
            query: {
                id,
            },
        } = this.$route;

        if (id && id != '' && id.length > 0) {
            this.id = id;
        }

        if (this.id != '' && this.id.length > 0) {
            this.getList();
        }

    },
    methods: {
        resetForm() {
            this.$refs['dataForm'].resetFields()
        },
        closeForm() {
            this.$emit('handleCloseViewDialog');
        },
        //格式化时间
        changeDateFormat: function (returnTime) {
            if (returnTime) {
                var dateee = new Date(returnTime).toJSON();
                var date = new Date(+new Date(dateee) + 8 * 3600 * 1000)
                    .toISOString()
                    .replace(/T/g, " ")
                    .replace(/\.[\d]{3}Z/, "");
                return date;
            } else {
                return "";
            }
        },
        getList() {
            // this.listLoading = true;
            let that = this;
            
           
        },

        async saveData() {
            var isValid = false;
            await this.$refs.dataForm.validate(valid => {
                isValid = valid;
            });
            if(!isValid) {
                this.$alert('页面有必填项未填写！', '提示', {
                    type: 'error'
                });
                return;
            }
            RESTAPI.saveDataFormConfigData(this.formData,
                response => {
                    this.listLoading = false;
                    if (
                        response.data &&
                        response.data.result === 20000 &&
                        response.data.data
                    ) {
                        this.$notify({
                            title: "提示",
                            message: "保存成功",
                            type: "success",
                            duration: 2000,
                            offset: 80,
                        });

                        // 添加成功后返回调用钩子函数，关闭弹窗 
                        this.$emit('handleSubmitSuccess');
                    } else {
                        this.$notify({
                            title: "警告",
                            message: "保存失败-401",
                            type: "warning",
                            duration: 2000,
                            offset: 80
                        });
                    }
                },
                error => {
                    // this.listLoading = false;
                    this.$notify({
                        title: "警告",
                        message: "保存失败-402" + error,
                        type: "warning",
                        duration: 2000,
                        offset: 80
                    });
                }
            );

        },
        
        submitForm() {
            this.$confirm("确定要提交吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                }).then(() => {
                    this.saveData();

                }
            ).catch(() => {});

        },


        //自动生成code
        handlerFormIdChange(value) {
            let lb = "";
            if (this.formData.LB == "QUERY") {
                lb = "_listCode";
            } else
            if (this.formData.LB == "EXECUTE") {
                lb = "_excuCode";
            }
            this.formData.CODE = value + lb;
        },

        //生成ARG
        crtARG() {
            let argarea = '{'; //生成后的字符串
            let argvalue = this.formData.ARGIN;
            let argArray = argvalue.split(",");
            if (argArray.length >= 1 && argArray[0] != '') {
                for (let i = 0; i < argArray.length; i++) {
                    argarea += '"';
                    argarea += argArray[i];
                    argarea += '":"${(';
                    argarea += argArray[i];
                    //argarea += '[0])!""}"';
                    argarea += ')!""}"';
                    if (i != argArray.length - 1 && argArray.length != 1) {
                        argarea += ',';
                    }
                }
            }
            argarea += '}';
            
            console.log(argarea);

            if (argArray.length == 1 && argArray[0] == '') {
                argarea = "";
            }
            this.formData.ARG = argarea;
			//强制刷新
            this.$forceUpdate();

        },
        //生成CON
        crtCON() {
            let conarea = '';
            let sfdt = 1; //1表示动态，0 表示非动态
            let conValue = this.formData.CONIN;;
            let conArray = conValue.split(",");

            let ifArr = [];
            if (!sfdt || sfdt == 0) { //非动态
                if (conArray.length >= 1 && conArray[0] != "") {
                    conarea += '[';
                    for (let i = 0; i < conArray.length; i++) {
                        conarea += '"${';
                        conarea += conArray[i];
                        conarea += '!""}"';
                        if (i != conArray.length - 1 && conArray.length != 1) {
                            conarea += ',';
                        }
                    }
                    conarea += ']';
                }
            } else { //动态
                if (conArray.length >= 1 && conArray[0] != "") {
                    conarea += '<@dynamics params=[';
                    let ifArr = [];
                    for (let i = 0; i < conArray.length; i++) {
                        conarea += '"${';
                        conarea += conArray[i];
                        conarea += '}"';
                        if (i != conArray.length - 1 && conArray.length != 1) { //判断是否加","
                            conarea += ',';
                        }

                        ifArr.push('<#if ' + conArray[i] + '?exists&' + conArray[i] + ' != ""> AND T.');
                        ifArr.push(conArray[i].toUpperCase() + ' LIKE ? </#if>\n');

                    }
                    conarea += '] />';

                    this.con_if_show = true;
                    this.formData.CON_IF = ifArr.join('');

                }

                //<#if jh?exists&jh!= "">AND T.F_ZBH LIKE ? </#if>


            }
            this.formData.CON = conarea;
            
            this.$forceUpdate();
            

        }
    }
}
</script>

<style scoped>

</style>