<template>
<div class="zhyy-list-container">
    <div class="zhyy-list-main">
        <el-row class="zhyy-list-searchAreaComplex" >
            <el-col :span="18">
                <label >表单名称：</label>
                <el-input @keyup.enter.native="processSearch" style="width: 200px;"
                    class="filter-item" placeholder="表单名称" v-model="listQuery.name" >
                </el-input>

                <label style="margin-left:8px;">表单ID：</label>
                <el-input @keyup.enter.native="processSearch" style="width: 200px;"
                    class="filter-item" placeholder="表单ID" v-model="listQuery.id" >
                </el-input>

                <el-button type="primary" icon="el-icon-search" style="margin-left:8px;"
                    @click="processSearch">查询</el-button>
                
                <span class="advancedBtn" ref="advancedBtn" @click="taggolExpand">
                    高级查询<i class="el-icon-d-arrow-left"></i>
                </span>



            </el-col>
            <el-col :span="6" style="text-align:right">
                <el-button type="success" icon="el-icon-document-add" @click="addData">新建</el-button>
                <el-button type="success" icon="el-icon-document-copy" @click="copyData">复制</el-button>
                <el-button type="danger" icon="el-icon-delete" @click="deleteData">删除</el-button>

            </el-col>
        </el-row>
        <!-- 淘宝查询组件区域 --> 
        <el-row class="searchAdvance">
          <tbsearch :tbData="tbData" @clickCB="clickCB" :isExpend="expend" :showBtn="false"></tbsearch>
        </el-row>
        
        <el-row class="zhyy-list-tableArea" >
            <el-table 
                :stripe="true" 
                :data="tableData" 
                v-loading="listLoading" 
                highlight-current-row
                style="width: 100%;" 
                ref="listTable"
                :row-key="getRowKey"
                :header-cell-style="{ background: '#F4F7FA'}"
                @row-click="onSelect">
                
                <el-table-column type="selection" width="50" align="center" :reserve-selection="true" fixed></el-table-column>
                <el-table-column align="center" label="序号" type="index" width="65" fixed>
                </el-table-column>
                <el-table-column width="350px" align="left" header-align="center" label="表单名称" fixed>
                    <template slot-scope="scope">
                        <span>{{scope.row.NAME}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="150px" align="center" header-align="center"  label="表单类型">
                    <template slot-scope="scope">
                        <span>{{scope.row.LBMC}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="150px" align="left" header-align="center" label="表单ID">
                    <template slot-scope="scope">
                        <span>{{scope.row.FORMID}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="150px" align="left" header-align="center" label="返回值代码">
                    <template slot-scope="scope">
                        <span>{{scope.row.CODE}}</span>
                    </template>
                </el-table-column>
                <el-table-column width="150px" align="center" header-align="center" label="录入时间">
                    <template slot-scope="scope">
                        <span>{{scope.row.CRTIME}}</span>
                    </template>
                </el-table-column>

                <!--注意如果是固定宽度并且操作列居右锁定时，操作列前面这列不要写width而是min-width，否则太宽时右边会出空白 -->
                <el-table-column min-width="150px" align="center" header-align="center" label="录入人">
                    <template slot-scope="scope">
                        <span>{{scope.row.CRTIME}}</span>
                    </template>
                </el-table-column>

                <el-table-column width="220px" label="操作" align="center" header-align="center" fixed="right">
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" icon="el-icon-edit"
                            @click="popEditData(scope.$index, scope.row)">弹出</el-button>
                        <el-button size="mini" type="text" icon="el-icon-edit"
                            @click="toEditData(scope.$index, scope.row)">跳转</el-button>
                        <el-button size="mini" type="text" icon="el-icon-delete"
                            @click="deleteData(scope.$index, scope.row)">删除</el-button>

                    </template>
                </el-table-column>
            </el-table>
            <div class="zhyy-list-paginationArea">
                <el-pagination background 
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" 
                    :page-sizes="[10, 20, 30, 50]" 
                    :page-size="listQuery.pageSize"
                    :current-page="listQuery.pageIndex"
                    layout="total, sizes, prev, pager, next, jumper" 
                    :total="total"
                    ></el-pagination>
            </div>
        </el-row>

    </div>

    
    <el-dialog  v-dialogDrag :visible.sync="getSubmitViewVisable" v-if="getSubmitViewVisable"
        :before-close="handleCloseViewDialog" 
        :title="editFormTitle"
        width="60%"
        :close-on-click-modal="false" >

        <editForm :id="actId" :newId="newId" 
        :operation="operation" 
        @handleSubmitSuccess="handleSubmitSuccess"  
        @handleCloseViewDialog="handleCloseViewDialog" />

    </el-dialog>

    <el-dialog  v-dialogDrag :visible.sync="popFormVisable" v-if="popFormVisable"
        :before-close="handleClosePopFormDialog" 
        title="简单弹出页" 
        width="880px"
        :close-on-click-modal="false" >

        <popForm :id="actId" :newId="newId" 
        :operation="operation" 
        @handleCloseViewDialog="handleClosePopFormDialog" />

    </el-dialog>


</div>
</template>

<script>
import editForm from "./zhyy-edit-dialog";
import popForm from "./zhyy-pop";
import { v4 as getUUID } from 'uuid';
/* import tbsearch from "../tools/TBSearch/index"; */
import restPath from "@/assets/configs/index";
import axios from 'axios';
import { Class } from "@vsui/lib-jsext";
import demoData from '@/mock/data_demo.js';

export default {
    
    name: "index",
    
    components: {
        editForm,
        popForm,
       /*  tbsearch, */

    },

    data() {
        return {
            title: "表单工具列表",

            total: null,
            listLoading: true,
            tableData: undefined,
            pkColumn: 'ID',
            listQuery: {
                pageIndex: 1,
                pageSize: 10,
                name: "",
                id: "",
            },

            editFormTitle:'',

            actId: '',
            operation: '',
            newId: '',
            selectedRow : undefined,
            getSubmitViewVisable: false,
            popFormVisable: false,

            expend:false,
            tbData: [],



        };
    },
    mounted() {
        this.loadListData();
        this.getTbData();

    },
    methods: {
        /**
		 * @description: 加载列表数据
		 * @param {*}
		 * @return {*}
		 */
        loadListData() {
            this.listLoading = false;
            this.tableData = [
                {
                    ID:'1',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'2',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'3',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'4',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'4',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },

                {
                    ID:'4',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },

                {
                    ID:'4',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },
                {
                    ID:'4',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },

                {
                    ID:'4',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },

                {
                    ID:'4',
                    NAME:'测试数据测试数据测试数据测试数据',
                    LBMC:'主表查询',
                    FORMID:'xxx',
                    CODE:'xxx',
                    CRTIME:'2021-09-09'
                },



            ];

            this.total = 1;
        },

        /**
		 * @description: 弹出页面提交后回调函数
		 * @param {*}
		 * @return {*}
		 */
        handleSubmitSuccess() {
            this.getSubmitViewVisable = false;
            this.loadListData();
        },

        /**
		 * @description: 弹出页面点击关闭按钮回调函数
		 * @param {*}
		 * @return {*}
		 */
        handleCloseViewDialog() {
            this.getSubmitViewVisable = false;
        },
        

        /**
		 * @description: 打开简单的弹出选择页
		 * @param {*}
		 * @return {*}
		 */
        popSelect() {
            this.popFormVisable = true;
        },
        /**
		 * @description: 关闭简单的弹出选择页
		 * @param {*}
		 * @return {*}
		 */
        handleClosePopFormDialog(done) {
            this.popFormVisable = false;
        },


        /**
		 * @description: 打开新增页面 - 弹出方式
		 * @param {*}
		 * @return {*}
		 */
        addData() {
            this.actId = getUUID().replace(/-/g, '').toUpperCase();
            this.editFormTitle = '新增表单信息';
            this.operation = 'add';
            this.getSubmitViewVisable = true;
        },

        /**
		 * @description: 打开编辑页面 - 弹出方式
		 * @param {*}
		 * @return {*}
		 */
        popEditData(index, row) {
            this.actId = row.ID;
            this.operation = 'edit';
            this.editFormTitle = '修改表单信息';
            this.getSubmitViewVisable = true;

        },
        /**
		 * @description: 打开编辑页面 - 跳转页面方式
		 * @param {*}
		 * @return {*}
		 */
        toEditData(index, row) {
            var pageName = 'zhyyEditForm';
            this.$router.push({
                name: pageName,
                path: '/' + pageName,
                params: {
                    id: row.ID
                }
            });

        },

        /**
		 * @description: 复制数据
		 * @param {*}
		 * @return {*}
		 */
        copyData() {
            let currentSelect = this.$refs.listTable.selection;
            console.log(currentSelect);
            if(currentSelect.length == 0){
                this.$notify({title: "提示", message: "请先选择要复制的行！", type: "info", duration: 2000, offset: 90});
                return;
            }
            if(currentSelect.length > 1){
                this.$notify({title: "提示", message: "请选择一行数据！", type: "info", duration: 2000, offset: 90});
                return;
            }
            this.actId = currentSelect[0].ID;
            this.editFormTitle = '复制表单信息';
            this.operation = 'copy';
            this.newId = getUUID().replace(/-/g, '').toUpperCase();
            this.getSubmitViewVisable = true;
        },

        /**
		 * @description: 删除数据
		 * @param {*}
		 * @return {*}
		 */
        deleteData(index, row) {
            console.log(JSON.stringify(row));

            this.$confirm("此操作将永久删除当前数据，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                
            })
            .then(() => {
                RESTAPI.delDataFormConfigData({
                        id: row.ID
                    },
                    response => {
                        if (response && response.data && response.data.result == 20000) {
                            this.$notify({
                                title: "提示",
                                message: "删除成功！",
                                type: "success",
                                duration: 2000,
                                offset: 80
                            });
                            this.loadListData();
                        } else {
                            this.$notify({
                                title: "警告",
                                message: "删除表单信息失败！" + +response.data.msg,
                                type: "warning",
                                duration: 2000,
                                offset: 80
                            });
                        }
                    },
                    error => {
                        this.$notify({
                            title: "警告",
                            message: "删除表单信息失败！" + error,
                            type: "warning",
                            duration: 2000,
                            offset: 80
                        });
                    }
                );

            })
            .catch(err => {
            });
        },

        /**
		 * @description: 查询数据，需要把页数重置为1
		 * @param {*}
		 * @return {*}
		 */
        processSearch() {
            this.listQuery.pageIndex = 1;
            this.loadListData();
        },

        /**
		 * @description: 变更每页条数时自动加载数据
		 * @param {*}
		 * @return {*}
		 */
        handleSizeChange(val) {
            this.listQuery.pageSize = val;
            this.loadListData();
        },
        
        /**
		 * @description: 点击某一页时自动加载数据
		 * @param {*}
		 * @return {*}
		 */
        handleCurrentChange(val) {
            this.listQuery.pageIndex = val;
            this.loadListData();
        },

        getRowKey(rowData){
            return rowData[this.pkColumn];
        }, 
        onSelect(val){
            //如果是单选
            var isSingleSelect = true;
            if(isSingleSelect){
                let currentSelect = this.$refs.listTable.selection;
                if(currentSelect.length > 0){
                    this.$refs.listTable.clearSelection();
                    if(currentSelect[0][this.pkColumn] != val[this.pkColumn]){
                        this.$refs.listTable.toggleRowSelection(val);
                    }
                }else{
                    this.$refs.listTable.toggleRowSelection(val);
                }
            }else{
                this.$refs.listTable.toggleRowSelection(val);

            }
            
            
        },


        /**
         * @Params: {{Params}}
         * @Description: Description
         */
        taggolExpand() {
            this.expend = !this.expend;
            if (this.expend) {
                Class.addClass("open", this.$refs.advancedBtn);
            } else {
                Class.removeClass("open", this.$refs.advancedBtn);
            }
        },

        //获取淘宝查询数据
        getTbData() {
            let data = demoData.TB_SEARCH();
            for (let i = 0; i < data.length; i++) {
                let tbObj = data[i];
                for (let j = 0; j < tbObj.sub.length; j++) {
                let sub = tbObj.sub[j];
                if (sub === null) {
                    tbObj.sub.splice(j, 1);
                }
                }
            }
            this.tbData = data;
            return false;
            axios.get(restPath.search_advance.getTaobaoData).then(response=>{
                let data = response.data;
                for (let i = 0; i < data.length; i++) {
                    let tbObj = data[i];
                    for (let j = 0; j < tbObj.sub.length; j++) {
                    let sub = tbObj.sub[j];
                    if (sub === null) {
                        tbObj.sub.splice(j, 1);
                    }
                    }
                }
                this.tbData = data;
            }).catch(e=>{
                this.$message({
                    message: "查询井基础表格数据出错",
                    type: "error"
                });
            })
        },
        /**
         * @Params: {{Params}}
         * @Description: 淘宝查询回调
         */
        clickCB(obj) {
            console.log('从淘宝查询区域传递过来的：', JSON.stringify(obj));

            let params = Object.assign({}, this.listQuery);
            
            var costAccountArr = [];
            if (obj.costAccount) {
                obj.costAccount.value.map(item => {
                    costAccountArr.push(item.value);
                });
            }
            params.costAccount = costAccountArr.join(',');

            console.log('组装后的查询参数：',  JSON.stringify(params));

            //调用查询方法
            //this.getWellBaseData();


        },

        
    },
    
};

</script>


<style scope>



</style>