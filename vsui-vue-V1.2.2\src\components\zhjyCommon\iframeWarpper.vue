<template>
    <el-card shadow="never" class="el-card-scoped">
        <div class="clearfix" slot="header" >
          <span class="" ref="title">{{_title}}</span>
          <el-button icon="fa fa-reply" class="rf el-button-scoped" v-if="(back!=''||_back!='undefined')" circle @click="goBack"></el-button>
          <el-button icon="fa fa-tv" class="rf el-button-scoped" circle @click="handleFullScreen" ></el-button>
          <el-button icon="fa fa-external-link-square" class="rf el-button-scoped" circle @click="blankOpen"></el-button>
          <el-button icon="fa fa-refresh" class="rf el-button-scoped el-button-refresh" :class="{ 'loading': loading }" circle @click="toRefresh"></el-button>
        </div>
        <el-container>
          <el-main class="el-main-scoped">
            <page-loader :url="_url" :watchQuery="watchQuery" ref="innerIframe" @framemounted="framemounted" @frameloaded="frameloaded"></page-loader>
          </el-main>
        </el-container>

    </el-card>

</template>
<style scoped>
/* 卡片 顶部蓝线 */

.el-card-scoped {
  box-sizing: border-box;
  border: unset;
  /* border-top: 2px solid #5fa1c6; */
  background-color: #fff;
  margin: 0 0;
  height: 100%;
}

/* 标签名称 字号和颜色 */

.el-card-scoped .title {
  font-size: 16px;
  color: #5e5e5e;
  font-weight: bold;
}


.el-card-scoped .el-card__body .el-container {
  height: 100%;
}
.iframeClass {
  position: relative;
  height: 100%;
  width: 100%;
  border: 0px;
}
</style>


<script>

/***
 * Author <EMAIL>
 * first add    unknown
 * 1：此IFRAME组件可以通过地址栏传入参数，如下：
 * url： 要在iframe加载的页面地址（需编码）
 * title： 此组件标题部位显示的文字（需编码）
 * back： 此组件返回时的页面地址，未指定在返回前一页面
 * modify 20190613 details:
 * 1：增加作为子组件时的传入参数（请参考props节点），为了支持此组件加载的页面存在JSAPI的情况，需要在父组件中实现JSAPI的处理
 * 2：约定URL传入参数与父组件传入参数的优先级，地址栏传入参数优先，父组件传入参数其次
 * 3：iframe的加载过程会触发两个事件：VUE生命周期的创建后事件：framemounted(innerframe)；iframe页面加载之后的frameloaded(innerframe)
 * 如果内部页面存在JSAPI，可通事件在外部注入API处理（域允许的前提下）
 * 4：增加对父组件传入参数变化的监听，父组件中动态修改了传入参数，则会在子组件中生效
 * by cuiliang 20190624
 * 该iframeWarpper组件由iframe组件独立出来，保留标题，按钮等交互操作，内部包含一个iframe.vue组件
 * 参数如下：
 * url:string 要加载的页面地址
 * title:string 组件标题
 * back:返回地址
 * 
 * 
 * 
 * 
 */


import {Uri} from "@vsui/lib-jsext";
import PageLoader from "@vsui/vue-components-page-loader";

export default {
  components: {
      PageLoader
    },
  data() {
    return {
      _url:"",
      _title:"",
      _back:"",
      loading:true,
    };
  },
  props: {
    url:
    {
      type: String,
      default:""
    },
    title:
    {
      type: String,
      default:""
    },
    back:
    {
      type: String,
      default:""
    },
    watchQuery:{
      type:Boolean,
      default:true
    }

  },
  computed:{
    
  },
  mounted() {
    const _this=this;
    this.$emit("framewarppermounted",_this)
  },
  created() {
    if(this.$route.query.url&&this.$route.query.url!="") this._url = decodeURIComponent(this.$route.query.url);
    if(this.url&&this.url!="") this._url = this.url;

    if(this.$route.query.title&&this.$route.query.title!="") this._title = decodeURIComponent(this.$route.query.title);
    if(this.$route.meta.title&&this.$route.meta.title!="") this._title = this.$route.meta.title;
    if(this.title&&this.title!="") this._title = this.title;
    
    if(this.$route.query.back&&this.$route.query.back!="") this._back = decodeURIComponent(this.$route.query.back);
    if(this.back&&this.back!="") this._back = this.back;
    
  },
  watch: {
    $route(to, from) {
      if(this.watchQuery&&to.query.url!=from.query.url)
      {
        this.loading=true;
        this.url=to.query.url;
      }
    },
    url(newV, oldV){
      this._url=newV;
      this.$refs.innerIframe.url=this._url
      
    },
    title(newV, oldV){
      this._title=newV;
    },
    back(newV, oldV){
      this._back=newV;
    }
  },
  methods: {
    goBack() {
      let backPage=this._back;
      let backRoute=backPage.indexOf("?")<0?backPage:backPage.substring(0,backPage.indexOf("?"));
      let backParams=Uri.getJsonFromUrlParams(backPage)||[];
      backPage ? this.$router.push(
        {
          path:backRoute,
          query:backParams
        }) : this.$router.back(-1);
    },
    blankOpen(){
      window.open(this._url,'_blank')
    },
    handleFullScreen(){
      let el = this.$refs.innerIframe.$el;
      var isFullscreen=document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen;
      if(!isFullscreen){//进入全屏,多重短路表达式
      (el.requestFullscreen&&el.requestFullscreen())||
      (el.mozRequestFullScreen&&el.mozRequestFullScreen())||
      (el.webkitRequestFullscreen&&el.webkitRequestFullscreen())||
      (el.msRequestFullscreen&&el.msRequestFullscreen())||
      (el.msRequestFullscreen&&el.msRequestFullscreen());

      }else{	//退出全屏,三目运算符
      document.exitFullscreen?document.exitFullscreen():
      document.mozCancelFullScreen?document.mozCancelFullScreen():
      document.webkitExitFullscreen?document.webkitExitFullscreen():
      document.msExitFullscreen?document.msExitFullscreen():"";
      }

    },
    frameloaded:function(iframe){
      this.loading=false;
      this.$emit("frameloaded",iframe)
    },
    framemounted:function(iframe){
      this.$emit("framemounted",iframe)
    },
    toRefresh(){
        this.loading=true;
        this.url=Uri.setParameter("d",new Date(),this._url);
    }
  }
};
</script>