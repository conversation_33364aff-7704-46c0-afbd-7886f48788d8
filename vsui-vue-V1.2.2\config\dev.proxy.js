module.exports = 
{
    '/backend':{
        target:'http://localhost:7777',
        changeOrigin:false,
        pathRewrite: {
          '^/backend': ''
        }
    },
        
    '/restful/api':{
      target:'http://localhost:8088',
        changeOrigin:true,
        pathRewrite: {
          '^/restful/api': '/apidata'
        }
    },
    /************@vsui/lib-vueauth4vseaf组件代理,SECURITY、CAS模式下使用此代理***************** */
    
    '/vseaf-service':
    {
      target:'http://localhost:7777',
        changeOrigin:false,
        pathRewrite: {
          '^/vseaf-service': '/'
        }
    },
   
    /************@vsui/lib-vueauth4vseaf组件代理,SIAM模式下使用此代理***************** */
    /*'/vseaf-service':
    {
      target:'http://127.0.0.1:8080',
        changeOrigin:false,
        pathRewrite: {
          '^/vseaf-service': '/'
        }
      },
      
    '/SSO':{
      target:'http://127.0.0.1:8080',
        changeOrigin:false,
        pathRewrite: {
          '^/SSO': '/SSO'
        }
      },*/
  
    /**********工作流示例的代理地址************************* */
    '/vsflow':{
      target:'http://*************:8888',
        changeOrigin:true,
        pathRewrite: {
          '^/vsflow': '/vsflow'
        }
    },
          
    
}