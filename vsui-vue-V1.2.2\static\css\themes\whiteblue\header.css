@import url("lefttopnavs.css");
/**************************头部样式开始*********************************/
.app .wrapper .header-box{
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .header {
    color: #fff;
    background-color: #ffffff;
    /* border-bottom:1px solid #7bb3cf; */
}
.app .wrapper .header-box.hidestate {

    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box.hidestate .showheader
{
    opacity: 1;
    color:#333333;
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .header .left-top-menu {
    border-right:1px solid #7bb3cf;
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .header .left-top-menu:hover {
    background: #1c6a92;
}


.app .wrapper .header-box .header .logo {
    background: url(../../../img/themes/whiteblue/logo.png);
    background-position: center;
    background-repeat: no-repeat;
    background-origin: content-box;
    /* background-size: 90px 40px; */
    background-size: 95px 35px;
    background-clip: content-box;
}
.app .wrapper .header-box .header .logo img{

}
.app .wrapper .header-box .header .sysName {

}

.app .wrapper .header-box .header .header-right >div {
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .header .header-right >div:first-child {
    border-right: 1px  solid #7bb3cf;
}
.app .wrapper .header-box .header .header-right >div:last-child {
    
}
.app .wrapper .header-box .header .header-right >div:hover {
    background: #1c6a92;
}
.app .wrapper .header-box .header .header-right .searchinfo
{

}
.app .wrapper .header-box .header .header-right .searchinfo .searchinput
{
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .header .header-right .searchinfo:hover .searchinput
{
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .header .header-right .searchinfo:hover .searchinput .el-input-group__append{
    background-color:#F2F2F2
}
.app .wrapper .header-box .header .header-right .searchinfo .searchinput input.el-input__inner
{
    /* background-color: #2384B7; */
    background-color: #0076F6;
    color: #fff;
}
.app .wrapper .header-box .header .header-right .searchinfo .searchinput button.searchbtn
{
    color:#333333;
}
.app .wrapper .header-box .header .header-right .timeinfo {
    
}

.app .wrapper .header-box .header .header-right .userinfo {
    
}
.app .wrapper .header-box .header .header-right .hideheader {
    
}
.app .wrapper .header-box .header .header-right .hidingheader{
    
}
.app .wrapper .header-box .header .header-right .fullscreen {
    
}
.app .wrapper .header-box .header .header-right .themeswitch {
    
}
.app .wrapper .header-box .header .header-right .messagehint {
    
}
.app .wrapper .header-box .header .header-right .setting{
    
}
.app .wrapper .header-box .header .header-right .logout {
    
}



/**********************上部通栏导航***************************/
.app .wrapper .header-box	.top-navs {
    background-color: #F2F2F2;
    border-bottom: 1px solid #cccccc;
    color:#333333;
}

.app .wrapper .header-box	.top-navs .el-menu-item-home
{
    border-right: 1px solid #cccccc;
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .top-navs .el-menu-item-home:hover
{
    color:#ffffff;
    /* background: #2384B7; */
    background-color: #0076F6;
}

.app .wrapper .header-box .top-navs .el-menu-scoped
{
    background-color: transparent !important;
}

.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped
{
    background-color: transparent;
    border-top: transparent solid 2px ;
    transition: all 0.2s ease-in-out;
    color:#333333;
    font-weight: 400;
}



.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped:hover
{
    /* background-color: #2384B7; */
    background-color: #0076F6;
    color: #fff;
}
.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active]
{
    color:#000000;
    font-weight: 700;
}
      
.app .wrapper .header-box .top-navs .el-menu-scoped .el-menu-item-scoped .el-menu-item-span-scoped
{
        
}
    
/*************************左上角可展开导航栏**********************************/
.app .wrapper .header-box .left-navs
{
    border-right:1px solid #7bb3cf;
    transition:all 0.2s ease-in-out;
    background: #F2F2F2;
    -webkit-transition: all 0.2s ease-in-out;
    color:#333333;
}
.app .wrapper .header-box .left-navs_collapse
{
}

.app .wrapper .header-box .themes-sel
{
    border-left: 1px solid #7bb3cf;
    border-bottom: 1px solid #7bb3cf;
    /* background: #2384B7; */
    background-color: #0076F6;
    color:#fff;
    transition:all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .themes-sel .theme_item
{
    
    border-right:1px solid #7bb3cf;
    border-bottom:1px solid #7bb3cf;
}
.app .wrapper .header-box .themes-sel .theme_item[class~=current_theme]
{
    background-color: #7bb3cf;
}
.app .wrapper .header-box .themes-sel .theme_item:hover
{
    background-color: #1c6a92;
    transition: all 0.2s ease-in-out;
}
.app .wrapper .header-box .themes-sel .theme_item:hover img
{
    opacity: .5;
}
/**************************头部样式完毕*********************************/

/********************加载条样式开始*****************************/
#nprogress {
    pointer-events: none;
  }
  
  #nprogress .bar {
    background: #F2F2F2;
  
    position: fixed;
    z-index: 1031;
    top: 0;
    left: 0;
  
    width: 100%;
    height: 2px;
  }
  
  /* Fancy blur effect */
  #nprogress .peg {
    display: block;
    position: absolute;
    right: 0px;
    width: 100px;
    height: 100%;
    box-shadow: 0 0 10px #F2F2F2, 0 0 5px #F2F2F2;
    opacity: 1.0;
  
    -webkit-transform: rotate(3deg) translate(0px, -4px);
        -ms-transform: rotate(3deg) translate(0px, -4px);
            transform: rotate(3deg) translate(0px, -4px);
  }
  
  /* Remove these to get rid of the spinner */
  #nprogress .spinner {
    display: block;
    position: fixed;
    z-index: 1031;
    top: 15px;
    right: 15px;
  }
  
  #nprogress .spinner-icon {
    width: 18px;
    height: 18px;
    box-sizing: border-box;
  
    border: solid 2px transparent;
    border-top-color: #F2F2F2;
    border-left-color: #F2F2F2;
    border-radius: 50%;
  
    -webkit-animation: nprogress-spinner 400ms linear infinite;
            animation: nprogress-spinner 400ms linear infinite;
  }
  
  .nprogress-custom-parent {
    overflow: hidden;
    position: relative;
  }
  
  .nprogress-custom-parent #nprogress .spinner,
  .nprogress-custom-parent #nprogress .bar {
    position: absolute;
  }
  
  @-webkit-keyframes nprogress-spinner {
    0%   { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
  }
  @keyframes nprogress-spinner {
    0%   { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
/****************加载条样式结束*********************************/