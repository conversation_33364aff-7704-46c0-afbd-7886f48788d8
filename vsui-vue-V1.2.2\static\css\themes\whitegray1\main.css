@import url("home.css");

/********************

本皮肤由崔良创建于20200415，

白蓝色：
白为主色调
蓝、灰为辅色调
字为黑色

黑色文字：#333333
灰色背景色：#F2F2F2
白色经过背景：#eeeeee
白色背景色：#ffffff
蓝色图标：#2384B7
灰色边线：#dddddd
黑色文字：#333333
鼠标经过黑色文字：#111111
菜单鼠标经过：#cccccc
选中文字：#000000




************************/





/********************************************以下为皮肤配色********************************************/
html,
body,
.app,
.wrapper {
  background-color: #F2F2F2;
  transition: all 0.3s ease-in-out;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
    background-color: #534c4c;

    /*width:4px;
    height:4px;*/
  }
  
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px #373737;


    background: #f6f6f6;

  }
::-webkit-scrollbar-track-piece
  {
    background-color: #eeeeee;
  }
  /*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 6px #373737;
    background-color: #F6F6F6;

    
  }
  ::-webkit-scrollbar-thumb:hover {
    -webkit-box-shadow: inset 0 0 6px #373737;
    background-color: #E7E7E7;

  }
  ::-webkit-scrollbar-corner{
    background: F6F6F6;
  }
  
  ::-webkit-scrollbar-button:horizontal:decrement {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #999;

  }
  ::-webkit-scrollbar-button:vertical:decrement {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #999;
  }
  ::-webkit-scrollbar-button:horizontal:decrement:hover {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #E7E7E7;
  }
  ::-webkit-scrollbar-button:vertical:decrement:hover {
    /*当鼠标在水平滚动条下面的按钮上的状态*/
    background-color: #E7E7E7;
  }
  .app {
    transition: all 0.3s ease-in-out;
    background-color:transparent;
    opacity:1;
    font-family: -apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif
  }
  .app.changingtheme {
    transition: all 0.3s ease-in-out;
    opacity:0;
  }

 