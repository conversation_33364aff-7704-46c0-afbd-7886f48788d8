
.app .wrapper .content-box .content .el-card-scoped > .el-card__body,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__body 
{
  padding: 0px;
  height:calc(100% - 40px) ;/*42是el-card__header的高度*/
}

.app .wrapper .content-box .content .el-card-scoped > .el-card__body .el-container ,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__body .el-container {
  position: relative;
  padding: 0px 0px;
  height:100%;
}

.app .wrapper .content-box .content .el-card-scoped > .el-card__body .el-container .el-main-scoped,
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__body .el-container .el-main-scoped {
  border: none;
  padding: 0px;
}

.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__body .el-container .el-main-scoped .exa_query {
  
    margin-top: 20px;
  }
    
.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__body .el-container .el-main-scoped .exa_query label {
    height: 32px;
    line-height: 32px;
    margin-left: 20px;
    padding-right: 5px;
  }

.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__body .el-container .pager {
    margin-top: 20px;
    margin-bottom: 30px;
    text-align: center;
  }

.app .wrapper .content-box .content .el-container-scoped .el-main-scoped .el-card-scoped > .el-card__body .el-container 
 .pager  .el-pagination-scoped
 {
     
 }

