/**********************左侧菜单*****************************/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped{
  border-right: 1px solid #666;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold {
  background: #333744;
  border-bottom: 1px solid #666;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold:hover{
  
}

/**和并展开小箭头**/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse{
  
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse:hover{
  background: #689DF4;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse>i {
  color: #fff;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse>i:hover {
  color: #fff;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts{

}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div {

}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div:hover {
  background: #689DF4;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div.shortcut {
  border: 1px solid #666;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div.plus {

  border: 1px dotted #666;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped {
  background-color: #333744;
  border-right: 0px solid #666666;
  color: #fff;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-submenu__title {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped.el-menu:not(.el-menu--collapse) {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped.el-menu--collapse {
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped{
  background-color: #333744;
  color: #fff; 
  }
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped:hover
{
  background-color: #689DF4;
  color: #fff; 
}
/*********左侧菜单图标字体设置*************/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped i{
  color:#fff;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped:hover .el-submenu-item-title-scoped{ 
  color: #fff !important; 
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped:hover i.iconfont{ 
  color: #fff !important; 
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active] .el-submenu-item-title-scoped{
  color: #689DF4;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active] i.iconfont{
  color: #689DF4;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title{
  background: transparent;
}
  .app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title i{
  color: #fff;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover{
  background: transparent;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover i {
  color: #fff;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu-item-title-scoped{
  color: #fff; 
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped {
  background: #333744;
}


.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content
{
  color:#fff;
  background: transparent;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content:hover{
  color: #fff !important;
  background-color: #689DF4 !important;
}


.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node[class*="is-current"] > .el-tree-node__content {
  color: #689DF4 ;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped i{
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped .custom-tree-node {
}
/* 左侧导航样式 结束 */