
/**
 * 
 *  
 * 
 * 
 */
 import {NameSpace,namespace,runtimeCfg} from "../core";
 /***
  * 此处用于定义主题信息，包含名称，皮肤路径（基准路径为：static/css/themes/）,示例图路径（基准路径为：static/img/themes/）
  */
 const themeInfo=
 {
     
     /**
      * 主题样式文件路径格式，外部使用String.format({0},{1}....{N})的方式格式化替换即可
      * 本字符串只有一个可替换元素为：{0}：主题path
      */
     themeFileUrl:(process.env.NODE_ENV === 'production'
         ? runtimeCfg.app_public_path
         //: buildConfig.devRootPath) //因开发模式下基路径定位方式与容器环境下不一致，所以修改此处
         : "/")+"static/css/themes/{0}/main.css",
     /**
      * 主题示例缩略图文件路径格式，外部使用String.format({0},{1}....{N})的方式格式化替换即可
      * 本字符串有两个可替换元素为：{0}：主题path，{1}：主题img
      */
     themeImgUrl:(process.env.NODE_ENV === 'production'
         ? runtimeCfg.app_public_path
         //: buildConfig.devRootPath)//因开发模式下基路径定位方式与容器环境下不一致，所以修改此处
         :"/")+"static/img/themes/{0}/{1}",
     /**
      * 主题列表信息,由外部注册
      */
     themes:NameSpace.getNameSpace(`${namespace}.themesdef`), 
     
 }
 
 export default themeInfo;