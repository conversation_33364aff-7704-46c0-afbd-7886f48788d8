'use strict'
require("@vsui/lib-jsext");
const path = require('path')
const utils = require('./utils')
const buildconfig = require('../config/index.js')
const vueLoaderConfig = require('./vue-loader.conf')
const webpack = require('webpack');
const VueLoaderPlugin = require('vue-loader/lib/plugin'); //引入这行

function resolve (dir) {
  return path.join(__dirname, '..', dir)
}


module.exports = {
  
  context: path.resolve(__dirname, '../'),
  
  output: {
    path: buildconfig.build.assetsRoot,
    filename: '[name].js',
    publicPath: process.env.NODE_ENV === 'production'? undefined :'/',
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: {
      'vue$': 'vue/dist/vue.esm.js',
      '@src': resolve('src'),
      '@lib': resolve('src/lib'),
      '@core': resolve('src/assets/core'),
      '@views': resolve('src/views'),
      '@components': resolve('src/components'),
      '@static': path.resolve(__dirname, '../static'),
    }
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: vueLoaderConfig
      },
      /*{
        test: /\.js$/,
        enforce: "pre",
        exclude: /node_modules/,
        include: [path.resolve(__dirname, 'src')],
        loader:"eslint-loader",
        options: { // 这里的配置项参数将会被传递到 eslint 的 CLIEngine 
          //formatter: require('eslint-friendly-formatter') // 指定错误报告的格式规范
        }
      },*/
      {
        test: /\.js$/,
        exclude: /node_modules/,
        include: [resolve('src'),resolve('examples'),resolve('node_modules')],
        use:{
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      },
      {
        test: /\.(png|jpeg|jpg|gif|svg|ico)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: utils.assetsPath('img/[name].[hash:7].[ext]')
        }
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: utils.assetsPath('media/[name].[hash:7].[ext]')
        }
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        loader: 'url-loader',
        options: {
          limit: 10000,
          name: utils.assetsPath('fonts/[name].[hash:7].[ext]')
        }
      }
    ]
  },
  plugins:[
    /*new webpack.DllReferencePlugin({
      context: path.resolve(__dirname, '..'),
      manifest: require('./vendor-manifest.json')
  }),*/
      new  webpack.ProvidePlugin({
        //$:'jquery',
        // jQuery:'jquery'
      }),
      new VueLoaderPlugin() //new一个实例
  ],
  node: {
    // prevent webpack from injecting useless setImmediate polyfill because Vue
    // source contains it (although only uses it if it's native).
    setImmediate: false,
    // prevent webpack from injecting mocks to Node native modules
    // that does not make sense for the client
    dgram: 'empty',
    fs: 'empty',
    net: 'empty',
    tls: 'empty',
    child_process: 'empty'
  }
}
