
import {router,store,axios,NameSpace,namespace,auth,runtimeCfg} from "../assets/core/index";
import VSAuth from "@vsui/lib-vueauth4vseaf";
//-----------开发模式下使用此鉴权配置，部署环境下请修改"static\js\runtime\config.js"文件内__vs_auth_config__节点------------
import VSAuthCfg from "./vsAuth.config.js";                                                                          ///
//----------------------------------------------------------------------------------------------------------------------

let vsAuth = NameSpace.getNameSpace(`${namespace}.vsAuth`);

const vsAuthConfig=process.env.NODE_ENV === 'production'?runtimeCfg.vs_auth_config:VSAuthCfg;

vsAuthConfig.router=router;//路由定义,鉴权组件会向路由定义中自动注入路由拦截
vsAuthConfig.store=store;//状态服务，登录过程会将信息写入vuex中
vsAuthConfig.axios=axios;
if(!vsAuth)
{   
    //这是胜软鉴权库4.4.2的初始化入口
    VSAuth.init(vsAuthConfig);
    auth.instance.init(VSAuth);
    NameSpace.setNameSpace(`${namespace}.vsAuth`,VSAuth);
}
 
export default VSAuth;