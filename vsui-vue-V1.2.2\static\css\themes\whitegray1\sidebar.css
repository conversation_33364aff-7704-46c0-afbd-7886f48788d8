/**********************左侧菜单*****************************/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped{
  border-right: 0px solid #f2f2f2;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs {
  background: transparent;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold {
  background: #ffffff;
  border-right: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold:hover{
  
}

/**和并展开小箭头**/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse{
  color:#2384B7;
  font-size: 14px;
  font-weight: normal;
  transition: all 0.1s ease-in-out;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse:hover{
  font-size: 18px;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse>i {
  
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .collapse>i:hover {
  
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts{

}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div {
  color:#333333
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div:hover {
  color: #111111;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div.shortcut {
  border: 1px solid #dddddd;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .left_navs_fold .shortcuts>div.plus {

  border: 1px dotted #dddddd;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped {
  background-color: #F2F2F2;
  border-right: 0px solid #dddddd;
  color: #333333;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-submenu__title {
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped.el-menu:not(.el-menu--collapse) {
  background: #FFFFFF;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped.el-menu--collapse {
  background: #FFFFFF;
  width:49px;
  border-right: 1px solid #f2f2f2;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped{
  background-color: #FFFFFF;
  color: #333333; 
  }
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped:hover
{
  background-color: #eeeeee;
  color: #111111; 
}
/*********左侧菜单图标字体设置*************/
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped i{
  color:#2384B7;
  width: 49px;
  font-size: 14px;
  transition: all 0.1s ease-in-out;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped:hover i{
  font-size: 16px;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped:hover .el-submenu-item-title-scoped{ 
  color: #111111 !important; 
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active]
{
  background-color: #F2F2F2;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active] .el-submenu-item-title-scoped{
  color: #000000;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title{
  color: #333333;
  background: transparent;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped[class~=is-active] .el-submenu__title i
{
  color: #1c6a92;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title i{
  color: #2384B7;
  font-size: 14px;
  transition: all 0.1s ease-in-out;
  font-weight: normal;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover{
  font-size: 16px;
  font-weight: normal;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu__title:hover i {
  font-size: 16px;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-submenu-item-title-scoped{
  color: #333333; 
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped {
  background: #F2F2F2;
}


.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content
{
  color:#333333;
  background: transparent;
}
.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content:hover{
  color: #111111 !important;
  background-color: #eeeeee !important;
}


.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node[class*="is-current"] > .el-tree-node__content {
  color: #000000 ;
  background-color: #F2F2F2;
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped i{
}

.app .wrapper .content-box .content .el-container-scoped .el-aside-scoped .left-navs .el-menu-scoped .el-menu-item-scoped .el-tree-scoped .el-tree-node .el-tree-node__content .el-tree-node-scoped .custom-tree-node {
}
/* 左侧导航样式 结束 */