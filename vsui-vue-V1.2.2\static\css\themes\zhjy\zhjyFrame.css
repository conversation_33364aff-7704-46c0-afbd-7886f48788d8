

/* 按钮动态效果 */
@import url("zhjyBuuton.css");

/* CDN 服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */



/* 框架页样式重写 */
*{
    padding: 0px;
    margin: 0px;
}

/* body属性 */
body{
    /* 修改鼠标指针 */
    cursor: url(m.svg),auto;
    /* cursor:url(https://www.xcsharp.top/read/mycur.png),auto; */
}

/* home页面样式开始 */
.frame_left{
    width: 250px;
    height: 100vh;
    float: left;
    overflow: auto;
    /* background-image: url(../../../img/zhjy/frame_left.png); */
    background-color: #255DCF;
    background-size: 100% 100%;
    background-repeat:no-repeat;
    transition: all 0.3s ease-in-out;
}

.frame_left_hide{
    width: 50px;
    height: 100vh;
    float: left;
    overflow: hidden;
    background-image: url(../../../img/zhjy/frame_left.png);
    background-size: auto 100%;
    background-repeat:no-repeat;
    transition: all 0.3s ease-in-out;
}

/* 右侧布局 */
.frame_right{
    width: calc(100% - 250px);
    height: 100vh;
    display: inline-block;
    background-color: #2F76FF;
    transition: all 0.3s ease-in-out;
}

.frame_right_hide{
    width: calc(100% - 50px);
    height: 100vh;
    display: inline-block;
    background-color: #2F76FF;
    transition: all 0.3s ease-in-out;
}


/* 左侧头部布局 */
.left_navs_fold_top{
    min-height: 140px;
}

/* 图标 */
.left_navs_fold_top_img{
    text-align: center;
}

.left_navs_fold_top_img img{
    height: 45px;
    margin: 18px 0px 12px 0px;
}


.left_navs_fold_top_title{
    font-size: 18px;
    color:#ffffff;
    text-align: center;
    letter-spacing: 6px;
    line-height: 28px;
    height: 28px;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
}

.left_navs_fold_top_content{
    font-size: 12px;
    color:#ffffff;
    text-align: center;
    letter-spacing: 2px;
    line-height: 20px;
    height: 20px;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
}


/* 头部样式 */
.app .wrapper .header-box .header{
    /* background-image: url(../../../img/zhjy/frame_head.png); */
    background-color: #ffffff;
    background-size: 100% 100%;
    background-repeat:no-repeat;
    background-position-x: -2px;
}

.app .wrapper .content-box .content .el-container-scoped .el-main-scoped{
    padding-top: 2px;
    height: calc(100vh - 50px);
}

.lf{
    color:#444444;
}


.header_showImg{
    height: 13px;
    width: 15px;
    margin-left: 20px;
    margin-right: 20px;
    cursor: url(mchoose.svg),auto;
}


.head_titleDiv{
    height: 100%;
    line-height: 48px;
}

.sysName{
    font-size: 16px;
}

.header_curPageTitle{
    color:#444444;
    font-size: 16px;
    letter-spacing: 2px;
    font-family: 'zhjyAlibabaThin';
    font-weight: 600;
}

.userinfo{
    cursor: pointer;
}

.userinfo img{
    height: 18px;
    width: 16px;
    vertical-align: text-bottom;
    margin-right: 10px;
}

.userinfo span{
    color: #444;
    font-size: 14px;
    font-family: 'zhjyAlibabaThin';
    letter-spacing: 1px;
}

/* 选项卡 */
.el-tabs__header{
    box-shadow: 1px 1px 4px #b3b3b3;
}


.el-tabs__header .is-active{
    background-color: #ffffff;
    border-bottom: 1px solid #ECF1FA !important;
    color: #444444 !important;
    font-size: 13px !important;
    font-family: 'zhjyAlibabaThin';
    letter-spacing: 1px;
    font-weight: 500 !important;
}

.el-tabs__header .is-closable{
    color: #444444 !important;
    font-size: 13px !important;
    font-family: 'zhjyAlibabaThin';
    letter-spacing: 1px;
    font-weight: 500 !important;
}


/* 
home页面样式结束
*/




  /* 列表页 */

  /* 查询条件 */
  .zhyy-list-searchArea label{
    color: #444;
    font-size: 13px;
    font-family: 'zhjyAlibabaThin';
  }

  /* 
  * ！！！！！！！！！！！
  * 列表高度样式，按一行查询固定，如需修改，请在页面重写覆盖该样式 
  */
  .zhyy-list-main .zhyy-list-tableArea{
    height: calc(100vh - 185px);
    border-radius: 5px;
  }

  .zhyy-list-main .zhyy-list-tableArea .el-table{
    height: calc(100% - 50px);
    border-radius: 5px;
  }


  /* 列表表头 */
  .zhyy-list-tableArea .el-table th{
    font-size: 14px;
    font-family: 'zhjyAlibabaThin';
    background-color: #ffffff !important;
  }

  /* 表头颜色 */
  .el-table th>.cell{
    color: #0158E9;
  }

 /* table的表头和内容的字体 */
  .el-table__header,
  .el-table__row {
    font-size: 13px;
    font-family: 'zhjyAlibabaThin';
    color:#000;
    letter-spacing: 0.3px;
  }

  /* 取消表格边线（不需要表格边线，自行解除注释，并将下方表格边线颜色替换注释） */
  /* .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf,
  .el-table--border th.el-table__cell, .el-table__fixed-right-patch,
  .el-table--border .el-table__cell, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed{
    border:0px !important;
  } 

  .el-table__footer-wrapper, .el-table__header-wrapper{
    border:1px solid #ebebeb;
  }*/

   /* 表格边线颜色替换 */
  .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf,
  .el-table--border th.el-table__cell, .el-table__fixed-right-patch,
  .el-table--border .el-table__cell, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed{
    border-right: 1px solid #dae4f8;
  }

  .el-table__footer-wrapper, .el-table__header-wrapper{
    border:0px;
  }
  

/*  调整斑马纹颜色 */
  .zhyy-list-tableArea .el-table .el-table__row--striped,
  .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
    background-color: #F6F8FD !important;
  }

  /* 修改选中背景色 */
  .el-table--striped .el-table__body tr.el-table__row--striped.current-row td.el-table__cell{
    background-color:   #E7ECF9 !important;
  }



/* 分页 */
.zhyy-list-paginationArea{
    /* 列表页面分页区域 */
    padding:8px;
    text-align: right;
  }
  
  .zhyy-list-paginationArea span{
    color: #4E4E4E;
    font-size: 13px !important;
}

.zhyy-list-paginationArea input{
    color:#3B6BF6;
    font-size: 13px !important;
}

.el-pagination .el-select .el-input .el-input__inner,
.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li,
.el-pagination__editor.el-input .el-input__inner{
    border-radius: 10px !important;
    box-shadow: 0.1px 0.1px 5px  #cdcdcd;
    border:0px;
}


.el-pagination .el-select .el-input .el-input__inner{
    box-shadow:none;
    border: none !important;
    padding-left:0px;
}

.el-pagination__jump .el-input__inner{
    width: 32px;
}

.el-pagination.is-background .el-pager li:not(.disabled).active{
    background-color: #3B6BF6 !important;
}

/* 分页弹窗 */
.el-select-dropdown__item{
    font-size: 12px;
    font-family: 'zhjyAlibabaThin';
}

/* 滚动条颜色 */
::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 0 0 3px #9dccf5 !important;
    background-color: #9dccf5 !important;
}


/* 表单 */

.el-input__inner{
    border:1px solid #DAE4F8 !important;
}


.button_div{
    height: 40px;
    position: absolute;
    bottom: 0px;
    padding: 8px;
    width: calc(100% - 55px);
    text-align: center;
    background-color: rgba(255,255, 255, 0.8);
    z-index: 2;
    border-radius: 8px;
}


