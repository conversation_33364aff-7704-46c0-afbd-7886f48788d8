<template>
    <div class="qlwz-list-container">
        <div class="qlwz-list-main">
            <div class="qlwz-list-searchArea">
                <h3 style="text-align: center;">管理员列表</h3>
                <el-row>
                    <el-col :span="18">
                        <label>标题:</label>
                        <el-input @keyup.enter.native="processSearch" style="width: 200px;" class="filter-item"
                            placeholder="标题" v-model="listQuery.infoTitle" clearable>
                        </el-input>
                        <el-button type="primary" icon="el-icon-search" style="margin-left:8px;"
                            @click="processSearch">查询</el-button>
                    </el-col>
                </el-row>
            </div>
            <el-row class="qlwz-list-tableArea">
                <el-table border v-loading="listLoading" :data="tableData" tooltip-effect="dark"
                    :header-cell-style="{ background: '#F4F7FA' }">
                    <el-table-column align="center" label="序号" type="index" min-width="5%" width="50px">
                    </el-table-column>
                    <el-table-column min-width="20%" align="left" header-align="center" label="标题" prop="infoTitle" show-overflow-tooltip>

                    </el-table-column>
                    <el-table-column min-width="10%" align="center" header-align="center" label="发布时间"
                        prop="publishTime">

                    </el-table-column>
                    <el-table-column v-if="this.type == 'JFKT'" min-width="10%" align="center" header-align="center"
                        label="上报单位" prop="operatorName">
                    </el-table-column>
                    <el-table-column v-if="type != 'JFKT'" min-width="10%" align="center" header-align="center"
                        label="当前节点" prop="processStatus">
                        <template slot-scope="scope">
                            <!-- 根据processStatus的值返回不同的字符串 -->
                            <span v-if="scope.row.processStatus === '0'">草稿</span>
                            <span v-if="scope.row.processStatus === '1'">纪检委员审核</span>
                            <span v-if="scope.row.processStatus === '2'">纪检部审核</span>
                            <span v-if="scope.row.processStatus === '3'">完成</span>
                            <span v-if="scope.row.processStatus === '4'">退回</span>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="10%" label="操作" align="center" header-align="center">
                        <template slot-scope="scope">
                            <!-- 查看按钮 -->
                            <el-button size="mini" type="text"
                                @click="openDialog(scope.row, scope.row.infoType === 'JFKT' ? '维护纪法课堂' : '维护信息门户')">【维护】</el-button>

                        </template>
                    </el-table-column>
                </el-table>
                <div class="qlwz-list-paginationArea">
                    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :page-sizes="[10, 20, 30, 50]" :page-size="listQuery.pageSize"
                        :current-page="listQuery.pageIndex" layout="total, sizes, prev, pager, next, jumper"
                        :total="total"></el-pagination>
                </div>
            </el-row>

        </div>
        <!-- 以下是管理员列表信息门户维护的弹出页 -->
        <el-dialog v-dialogDrag v-if="dialog" width="40%" :title="Title" :visible.sync="dialog"
            :before-close="handleCloseViewDialog" :close-on-click-modal="false">
            <whxxmh :rowData="rowData" @handleCloseViewDialog="handleCloseViewDialog" />
        </el-dialog>
        <!-- 以下是管理员列表纪法课堂维护的弹出页 -->
        <el-dialog v-dialogDrag v-if="JFKTDialog" width="45%" :title="Title" :visible.sync="JFKTDialog"
            :before-close="handleCloseViewDialog" :close-on-click-modal="false">
            <whjfkt :rowData="rowData" @handleCloseViewDialog="handleCloseViewDialog" />
        </el-dialog>
    </div>
</template>
<script>
import whxxmh from './whxxmh.vue';
import whjfkt from "./whjfkt.vue";
export default {
    components: {
        whxxmh, whjfkt
    },
    data() {
        return {
            rowData: {},
            total: null,
            tableData: [],
            listLoading: true,
            listQuery: {
                pageIndex: 1,
                pageSize: 10,
                processStatusArray: [],
            },
            //对话框相关变量
            dialog: false,
            Title: '',
            JFKTDialog: false,
        }
    },
    watch: {
        $route: {
            handler(oldVal) {
                this.loadListData();
            },
            deep: true
        },
    },
    mounted() {
        this.loadListData();
    },
    methods: {
        //加载数据列表
        loadListData() {
            this.listQuery.processStatusArray = ['1', '2', '3'];
            this.axios.post('/backend/qlwz/queryInfoDataList', this.listQuery
            ).then(resp => {
                if (resp.data.data) {
                    this.tableData = resp.data.data.rows;
                    this.total = resp.data.data.total;
                    this.listLoading = false;
                }
            }).catch((error) => {
                this.$message.error('查询失败')
                this.listLoading = false;
            });

        },
        // 打开对话框页面
        openDialog(row, title) {
            this.rowData = row;
            this.Title = title;
            if (row.infoType == 'JFKT') {
                this.JFKTDialog = true;

            } else {
                this.dialog = true;
            }

        },
        // 弹出页面点击关闭按钮回调函数
        handleCloseViewDialog() {
            this.dialog = false;
            this.JFKTDialog = false;
            this.loadListData();
        },

        //查询数据
        processSearch() {
            this.listQuery.pageIndex = 1;
            this.loadListData();
        },
        // 变更每页条数时自动加载数据
        handleSizeChange(val) {
            this.listQuery.pageSize = val;
            this.loadListData();
        },

        // 点击某一页时自动加载数据
        handleCurrentChange(val) {
            this.listQuery.pageIndex = val;
            this.loadListData();
        },



    }
}
</script>
<style scoped></style>