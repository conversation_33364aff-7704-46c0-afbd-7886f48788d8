<!--
 * @Author: your name
 * @Date: 2021-07-15 15:46:11
 * @LastEditTime: 2021-07-15 16:15:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \vsui.vue-V1.1.0\examples\components\page\example_pages\example-search\zhyy-transfer.vue
-->
<template>
    <div style="text-align: center;">
        <el-transfer
      style="text-align: left; display: inline-block"
      v-model="value"
      filterable
      :right-default-checked="[1]"
      :render-content="renderFunc"
      :titles="['待选数据', '已选数据']"
      :format="{
        noChecked: '${total}',
        hasChecked: '${checked}/${total}'
      }"
      @change="handleChange"
      :data="data">
    </el-transfer>
        <el-row >
            <el-col :span="24" style="text-align:center;padding-top:12px">
                <el-button type="success" icon="el-icon-check" @click="confirmSelect">确定</el-button>
                <el-button type="danger" icon="el-icon-delete" @click="clearSelect">清空</el-button>

            </el-col>
        </el-row>
        
    </div>

</template>
  
  <script>
    export default {
      data() {
        const generateData = _ => {
          const data = [];
          for (let i = 1; i <= 15; i++) {
            data.push({
              key: i,
              label: `备选项 ${ i }`,
              //disabled: i % 4 === 0
            });
          }
          return data;
        };
        return {
          data: generateData(),
          value: [1, 4],
          

        };
      },

      methods: {
        confirmSelect(){
            this.$emit("confirmSelect", this.value);
        },

        clearSelect(){
            this.$confirm("确定清空父页面数据？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
            .then(() => {
                this.$emit("clearSelect");
            })
            .catch(err => {
            });
            
        },

      }
    };
  </script>